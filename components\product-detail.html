<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品详情</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="bg-gray-50">
    <div class="page-content">
        <!-- 商品图片轮播 -->
        <div class="relative h-80 bg-white">
            <img src="https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=320&fit=crop" 
                 alt="Product" class="w-full h-full object-cover">
            <div class="absolute top-4 left-4 right-4 flex justify-between">
                <div class="w-8 h-8 bg-black/50 rounded-full flex items-center justify-center">
                    <i class="fas fa-arrow-left text-white"></i>
                </div>
                <div class="flex space-x-2">
                    <div class="w-8 h-8 bg-black/50 rounded-full flex items-center justify-center">
                        <i class="fas fa-share text-white"></i>
                    </div>
                    <div class="w-8 h-8 bg-black/50 rounded-full flex items-center justify-center">
                        <i class="fas fa-heart text-white"></i>
                    </div>
                </div>
            </div>
            <div class="absolute bottom-4 right-4 bg-black/50 text-white px-2 py-1 rounded text-xs">
                1/5
            </div>
        </div>

        <!-- 商品基本信息 -->
        <div class="bg-white p-4 border-b border-gray-100">
            <div class="flex items-start justify-between mb-2">
                <h1 class="text-lg font-bold text-gray-800 flex-1 mr-4">时尚智能手表 - 健康监测运动手表</h1>
                <div class="flex items-center text-sm text-gray-500">
                    <i class="fas fa-star text-yellow-400 mr-1"></i>
                    <span>4.8</span>
                </div>
            </div>
            <div class="flex items-center mb-3">
                <span class="text-2xl font-bold text-red-500 mr-3">¥599.00</span>
                <span class="text-sm text-gray-400 line-through mr-2">¥899.00</span>
                <span class="bg-red-100 text-red-600 px-2 py-1 rounded text-xs">限时优惠</span>
            </div>
            <div class="flex items-center text-sm text-gray-600">
                <span class="mr-4">已售 2.3k</span>
                <span class="mr-4">库存 156</span>
                <span>包邮</span>
            </div>
        </div>

        <!-- 社交推荐 -->
        <div class="bg-white p-4 border-b border-gray-100">
            <h3 class="font-bold text-gray-800 mb-3">朋友推荐</h3>
            <div class="flex items-center mb-3">
                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=30&h=30&fit=crop&crop=face" 
                     class="w-8 h-8 rounded-full mr-3">
                <div class="flex-1">
                    <p class="text-sm text-gray-800">李小美 推荐了这款商品</p>
                    <p class="text-xs text-gray-500">质量很好，值得购买！</p>
                </div>
                <div class="flex items-center text-xs text-gray-400">
                    <i class="fas fa-heart text-red-500 mr-1"></i>
                    <span>128</span>
                </div>
            </div>
            <div class="flex -space-x-2">
                <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=24&h=24&fit=crop&crop=face" 
                     class="w-6 h-6 rounded-full border-2 border-white">
                <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=24&h=24&fit=crop&crop=face" 
                     class="w-6 h-6 rounded-full border-2 border-white">
                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=24&h=24&fit=crop&crop=face" 
                     class="w-6 h-6 rounded-full border-2 border-white">
                <div class="w-6 h-6 bg-gray-200 rounded-full border-2 border-white flex items-center justify-center">
                    <span class="text-xs text-gray-600">+5</span>
                </div>
            </div>
        </div>

        <!-- 规格选择 -->
        <div class="bg-white p-4 border-b border-gray-100">
            <div class="mb-4">
                <h4 class="font-semibold text-gray-800 mb-2">颜色</h4>
                <div class="flex space-x-2">
                    <div class="px-3 py-2 border-2 border-blue-500 bg-blue-50 rounded-lg text-sm text-blue-600">
                        黑色
                    </div>
                    <div class="px-3 py-2 border border-gray-200 rounded-lg text-sm text-gray-600">
                        白色
                    </div>
                    <div class="px-3 py-2 border border-gray-200 rounded-lg text-sm text-gray-600">
                        玫瑰金
                    </div>
                </div>
            </div>
            <div>
                <h4 class="font-semibold text-gray-800 mb-2">尺寸</h4>
                <div class="flex space-x-2">
                    <div class="px-3 py-2 border-2 border-blue-500 bg-blue-50 rounded-lg text-sm text-blue-600">
                        42mm
                    </div>
                    <div class="px-3 py-2 border border-gray-200 rounded-lg text-sm text-gray-600">
                        46mm
                    </div>
                </div>
            </div>
        </div>

        <!-- 商品详情 -->
        <div class="bg-white p-4 border-b border-gray-100">
            <h3 class="font-bold text-gray-800 mb-3">商品详情</h3>
            <div class="space-y-2 text-sm text-gray-600">
                <p>• 1.4英寸高清彩色显示屏</p>
                <p>• 心率监测、血氧检测</p>
                <p>• 50米防水设计</p>
                <p>• 7天超长续航</p>
                <p>• 支持100+运动模式</p>
                <p>• 智能通知提醒</p>
            </div>
        </div>

        <!-- 用户评价 -->
        <div class="bg-white p-4">
            <div class="flex items-center justify-between mb-3">
                <h3 class="font-bold text-gray-800">用户评价 (1.2k)</h3>
                <span class="text-sm text-blue-500">查看全部</span>
            </div>
            
            <div class="space-y-4">
                <div class="border-b border-gray-100 pb-4">
                    <div class="flex items-center mb-2">
                        <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=30&h=30&fit=crop&crop=face" 
                             class="w-8 h-8 rounded-full mr-3">
                        <div class="flex-1">
                            <h5 class="font-semibold text-sm">张小明</h5>
                            <div class="flex items-center">
                                <div class="flex text-yellow-400 mr-2">
                                    <i class="fas fa-star text-xs"></i>
                                    <i class="fas fa-star text-xs"></i>
                                    <i class="fas fa-star text-xs"></i>
                                    <i class="fas fa-star text-xs"></i>
                                    <i class="fas fa-star text-xs"></i>
                                </div>
                                <span class="text-xs text-gray-500">3天前</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-sm text-gray-700 mb-2">手表质量很好，功能齐全，续航能力强，性价比很高！</p>
                    <div class="flex space-x-2">
                        <img src="https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=60&h=60&fit=crop" 
                             class="w-12 h-12 rounded-lg">
                        <img src="https://images.unsplash.com/photo-1434056886845-dac89ffe9b56?w=60&h=60&fit=crop" 
                             class="w-12 h-12 rounded-lg">
                    </div>
                </div>
                
                <div>
                    <div class="flex items-center mb-2">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=30&h=30&fit=crop&crop=face" 
                             class="w-8 h-8 rounded-full mr-3">
                        <div class="flex-1">
                            <h5 class="font-semibold text-sm">李小美</h5>
                            <div class="flex items-center">
                                <div class="flex text-yellow-400 mr-2">
                                    <i class="fas fa-star text-xs"></i>
                                    <i class="fas fa-star text-xs"></i>
                                    <i class="fas fa-star text-xs"></i>
                                    <i class="fas fa-star text-xs"></i>
                                    <i class="fas fa-star text-xs"></i>
                                </div>
                                <span class="text-xs text-gray-500">1周前</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-sm text-gray-700">外观很漂亮，运动监测很准确，推荐购买！</p>
                </div>
            </div>
        </div>

        <!-- 底部操作栏 -->
        <div class="fixed bottom-20 left-0 right-0 bg-white border-t border-gray-100 px-4 py-3 shadow-lg">
            <div class="flex items-center space-x-3">
                <div class="flex items-center space-x-4">
                    <div class="text-center">
                        <i class="fas fa-store text-gray-600 text-lg"></i>
                        <p class="text-xs text-gray-600 mt-1">店铺</p>
                    </div>
                    <div class="text-center">
                        <i class="fas fa-comments text-gray-600 text-lg"></i>
                        <p class="text-xs text-gray-600 mt-1">客服</p>
                    </div>
                    <div class="text-center">
                        <i class="fas fa-heart text-red-500 text-lg"></i>
                        <p class="text-xs text-gray-600 mt-1">收藏</p>
                    </div>
                </div>
                <div class="flex-1 flex space-x-2">
                    <button class="flex-1 bg-orange-500 text-white py-3 rounded-full font-semibold">
                        加入购物车
                    </button>
                    <button class="flex-1 bg-red-500 text-white py-3 rounded-full font-semibold">
                        立即购买
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

// Vue 3 应用配置
const { createApp, ref, reactive, computed, onMounted } = Vue;

// 全局状态管理
const globalState = reactive({
  currentTab: 'home',
  user: {
    name: '张小明',
    avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=100&h=100&fit=crop&crop=face',
    followers: 1234,
    following: 567
  },
  cart: {
    items: [],
    total: 0
  },
  notifications: 3
});

// 工具函数
const utils = {
  formatPrice: (price) => `¥${price.toFixed(2)}`,
  formatNumber: (num) => {
    if (num >= 10000) {
      return `${(num / 10000).toFixed(1)}万`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`;
    }
    return num.toString();
  },
  getCurrentTime: () => {
    const now = new Date();
    return now.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
  }
};

// Tab切换功能
function switchTab(tabName) {
  globalState.currentTab = tabName;
  
  // 更新所有iframe的显示状态
  const iframes = document.querySelectorAll('.page-iframe');
  iframes.forEach(iframe => {
    if (iframe.id === `${tabName}-frame`) {
      iframe.style.display = 'block';
    } else {
      iframe.style.display = 'none';
    }
  });
  
  // 更新tab bar状态
  const tabItems = document.querySelectorAll('.tab-item');
  tabItems.forEach(item => {
    if (item.dataset.tab === tabName) {
      item.classList.add('active');
    } else {
      item.classList.remove('active');
    }
  });
}

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
  // 更新状态栏时间
  function updateStatusBar() {
    const timeElement = document.querySelector('.status-time');
    if (timeElement) {
      timeElement.textContent = utils.getCurrentTime();
    }
  }
  
  // 每分钟更新一次时间
  updateStatusBar();
  setInterval(updateStatusBar, 60000);
  
  // 初始化默认tab
  switchTab('home');
  
  // 绑定tab点击事件
  const tabItems = document.querySelectorAll('.tab-item');
  tabItems.forEach(item => {
    item.addEventListener('click', () => {
      const tabName = item.dataset.tab;
      switchTab(tabName);
    });
  });
});

// 导出全局对象供组件使用
window.AppGlobal = {
  state: globalState,
  utils: utils,
  switchTab: switchTab
};

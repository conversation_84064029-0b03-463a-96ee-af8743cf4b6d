<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>Home - Podcast Prototype</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" />
  <style>
    :root{--safe-top: env(safe-area-inset-top); --safe-bottom: env(safe-area-inset-bottom);}
    body { font-family: Inter, ui-sans-serif, system-ui; background: #f8fafc; color:#0b1220; -webkit-font-smoothing:antialiased;}
    .app { height:100vh; display:flex; flex-direction:column; }
    .status { height: 44px; display:flex; align-items:center; justify-content:space-between; padding:0 12px; font-size:12px; color:#475569; background:linear-gradient(180deg,#f8fafc,#f1f5f9);}
    .nav { height:56px; display:flex; align-items:center; justify-content:space-between; padding:0 16px; }
    .safe { padding-bottom: calc(var(--safe-bottom) + 12px); }
    .chip { background:#eef2ff; color:#3730a3; padding:6px 10px; border-radius:999px; font-weight:600; font-size:13px; }
  </style>
</head>
<body>
  <div class="app rounded-t-lg">
    <!-- 模拟 iOS 状态栏 -->
    <div class="status">
      <div class="flex items-center gap-2"><span>9:41</span></div>
      <div class="flex items-center gap-2"><i class="fa-solid fa-signal"></i><i class="fa-solid fa-wifi"></i><i class="fa-solid fa-battery-full"></i></div>
    </div>

    <!-- 导航 -->
    <div class="nav">
      <div class="flex items-center gap-3">
        <img src="https://images.unsplash.com/photo-1544005313-94ddf0286df2?q=80&w=200&auto=format&fit=crop&ixlib=rb-4.0.3&s=abb3d26b3d4b6f8e8b8b8fb6d6b2b4d9" alt="avatar" class="w-10 h-10 rounded-full object-cover" />
        <div>
          <div class="text-sm font-semibold">你好，听友</div>
          <div class="text-xs text-slate-500">为你推荐今日精彩</div>
        </div>
      </div>
      <div class="flex items-center gap-3">
        <button class="chip"><i class="fa-solid fa-bell"></i></button>
        <button class="p-2 rounded-lg bg-white shadow"><i class="fa-solid fa-search"></i></button>
      </div>
    </div>

    <!-- 内容 -->
    <div class="p-4 overflow-auto flex-1">
      <!-- 订阅横向 -->
      <div>
        <div class="flex items-center justify-between mb-3">
          <div class="text-sm font-semibold">我的订阅</div>
          <div class="text-xs text-slate-500">查看全部</div>
        </div>
        <div class="flex gap-3 overflow-x-auto pb-2">
          <!-- subscription card -->
          <div class="min-w-[140px] bg-white rounded-2xl shadow p-3">
            <img class="w-full h-28 rounded-xl object-cover" src="https://images.unsplash.com/photo-1509316785287-8c1d2f1d1c48?q=80&w=800&auto=format&fit=crop" alt="cover"/>
            <div class="mt-2 text-sm font-semibold">清晨故事</div>
            <div class="text-xs text-slate-500">更新到 第240期</div>
          </div>
          <div class="min-w-[140px] bg-white rounded-2xl shadow p-3">
            <img class="w-full h-28 rounded-xl object-cover" src="https://images.unsplash.com/photo-1497215728101-856f4ea42174?q=80&w=800&auto=format&fit=crop" alt="cover"/>
            <div class="mt-2 text-sm font-semibold">科技漫谈</div>
            <div class="text-xs text-slate-500">更新到 第8期</div>
          </div>
          <div class="min-w-[140px] bg-white rounded-2xl shadow p-3">
            <img class="w-full h-28 rounded-xl object-cover" src="https://images.unsplash.com/photo-1526378729826-3c3c6b5d9b0b?q=80&w=800&auto=format&fit=crop" alt="cover"/>
            <div class="mt-2 text-sm font-semibold">旅行日记</div>
            <div class="text-xs text-slate-500">更新到 第67期</div>
          </div>
        </div>
      </div>

      <!-- 推荐列表 -->
      <div class="mt-4">
        <div class="flex items-center justify-between mb-3">
          <div class="text-sm font-semibold">为你推荐</div>
          <div class="text-xs text-slate-500">基于你的订阅</div>
        </div>

        <div class="space-y-3">
          <!-- episode card -->
          <div class="bg-white rounded-2xl p-4 flex gap-4 shadow">
            <img src="https://images.unsplash.com/photo-1518779578993-ec3579fee39f?q=80&w=800&auto=format&fit=crop" class="w-20 h-20 rounded-lg object-cover" />
            <div class="flex-1">
              <div class="flex items-start justify-between">
                <div>
                  <div class="text-sm font-semibold">如何提高专注力</div>
                  <div class="text-xs text-slate-500 mt-1">45 分钟 · 科学方法 · 主讲：李老师</div>
                </div>
                <div class="text-right">
                  <button class="bg-indigo-600 text-white px-3 py-1 rounded-lg text-sm"><i class="fa-solid fa-play"></i></button>
                </div>
              </div>
              <div class="mt-3 text-xs text-slate-400">播放 12.3k · 评论 256</div>
            </div>
          </div>

          <div class="bg-white rounded-2xl p-4 flex gap-4 shadow">
            <img src="https://images.unsplash.com/photo-1504384308090-c894fdcc538d?q=80&w=800&auto=format&fit=crop" class="w-20 h-20 rounded-lg object-cover" />
            <div class="flex-1">
              <div class="flex items-start justify-between">
                <div>
                  <div class="text-sm font-semibold">夜读·经典名著</div>
                  <div class="text-xs text-slate-500 mt-1">30 分钟 · 文学 · 主讲：张编辑</div>
                </div>
                <div class="text-right">
                  <button class="bg-indigo-600 text-white px-3 py-1 rounded-lg text-sm"><i class="fa-solid fa-play"></i></button>
                </div>
              </div>
              <div class="mt-3 text-xs text-slate-400">播放 8.9k · 收藏 1.2k</div>
            </div>
          </div>

        </div>
      </div>

    </div>

    <!-- 底部 Tab Bar -->
    <div class="bg-white border-t p-3 safe">
      <div class="flex justify-between items-center">
        <div class="text-xs text-slate-500">正在播放：无</div>
        <div class="flex items-center gap-4">
          <button class="text-slate-600"><i class="fa-solid fa-house"></i></button>
          <button class="text-slate-600"><i class="fa-solid fa-compass"></i></button>
          <button class="text-slate-600"><i class="fa-solid fa-play-circle"></i></button>
          <button class="text-slate-600"><i class="fa-solid fa-user"></i></button>
        </div>
      </div>
    </div>
  </div>
</body>
</html>
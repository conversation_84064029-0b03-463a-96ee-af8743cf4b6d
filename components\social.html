<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>社交</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="bg-gray-50">
    <div class="page-content">
        <!-- 顶部导航 -->
        <div class="bg-white px-4 py-3 shadow-sm">
            <div class="flex items-center justify-between">
                <h1 class="text-xl font-bold text-gray-800">消息</h1>
                <div class="flex items-center space-x-3">
                    <i class="fas fa-user-plus text-gray-600"></i>
                    <i class="fas fa-edit text-gray-600"></i>
                </div>
            </div>
        </div>

        <!-- 搜索栏 -->
        <div class="px-4 py-3">
            <div class="flex items-center bg-gray-100 rounded-full px-4 py-2">
                <i class="fas fa-search text-gray-400 mr-3"></i>
                <input type="text" placeholder="搜索好友或群聊..." class="flex-1 bg-transparent outline-none text-sm">
            </div>
        </div>

        <!-- 快捷功能 -->
        <div class="px-4 pb-3">
            <div class="flex space-x-4">
                <div class="flex-1 bg-white rounded-2xl p-4 shadow-sm text-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-users text-blue-500 text-lg"></i>
                    </div>
                    <span class="text-sm text-gray-600">群聊</span>
                </div>
                <div class="flex-1 bg-white rounded-2xl p-4 shadow-sm text-center">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-heart text-green-500 text-lg"></i>
                    </div>
                    <span class="text-sm text-gray-600">关注</span>
                </div>
                <div class="flex-1 bg-white rounded-2xl p-4 shadow-sm text-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-star text-purple-500 text-lg"></i>
                    </div>
                    <span class="text-sm text-gray-600">收藏</span>
                </div>
            </div>
        </div>

        <!-- 聊天列表 -->
        <div class="px-4">
            <h2 class="text-lg font-bold text-gray-800 mb-3">最近聊天</h2>
            
            <!-- 聊天项1 -->
            <div class="bg-white rounded-2xl mb-2 shadow-sm">
                <div class="flex items-center p-4">
                    <div class="relative mr-3">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=50&h=50&fit=crop&crop=face" 
                             class="w-12 h-12 rounded-full">
                        <div class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-xs">3</span>
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-1">
                            <h4 class="font-semibold text-sm">李小美</h4>
                            <span class="text-xs text-gray-500">10:30</span>
                        </div>
                        <p class="text-sm text-gray-600 truncate">那个包包真的很好看，你在哪里买的？</p>
                    </div>
                </div>
            </div>

            <!-- 聊天项2 -->
            <div class="bg-white rounded-2xl mb-2 shadow-sm">
                <div class="flex items-center p-4">
                    <div class="relative mr-3">
                        <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=50&h=50&fit=crop&crop=face" 
                             class="w-12 h-12 rounded-full">
                        <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-1">
                            <h4 class="font-semibold text-sm">张小明</h4>
                            <span class="text-xs text-gray-500">昨天</span>
                        </div>
                        <p class="text-sm text-gray-600 truncate">好的，明天见！</p>
                    </div>
                </div>
            </div>

            <!-- 群聊项 -->
            <div class="bg-white rounded-2xl mb-2 shadow-sm">
                <div class="flex items-center p-4">
                    <div class="relative mr-3">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-users text-white"></i>
                        </div>
                        <div class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-xs">9</span>
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-1">
                            <h4 class="font-semibold text-sm">购物分享群</h4>
                            <span class="text-xs text-gray-500">09:45</span>
                        </div>
                        <p class="text-sm text-gray-600 truncate">王小花: 今天有什么好的推荐吗？</p>
                    </div>
                </div>
            </div>

            <!-- 聊天项3 -->
            <div class="bg-white rounded-2xl mb-2 shadow-sm">
                <div class="flex items-center p-4">
                    <div class="relative mr-3">
                        <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=50&h=50&fit=crop&crop=face" 
                             class="w-12 h-12 rounded-full">
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-1">
                            <h4 class="font-semibold text-sm">美食家小丽</h4>
                            <span class="text-xs text-gray-500">周二</span>
                        </div>
                        <p class="text-sm text-gray-600 truncate">谢谢你的推荐，味道真的很棒！</p>
                    </div>
                </div>
            </div>

            <!-- 聊天项4 -->
            <div class="bg-white rounded-2xl mb-2 shadow-sm">
                <div class="flex items-center p-4">
                    <div class="relative mr-3">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=face" 
                             class="w-12 h-12 rounded-full">
                        <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-1">
                            <h4 class="font-semibold text-sm">数码达人阿明</h4>
                            <span class="text-xs text-gray-500">周一</span>
                        </div>
                        <p class="text-sm text-gray-600 truncate">这个耳机的音质确实不错</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 推荐好友 -->
        <div class="px-4 py-3">
            <h2 class="text-lg font-bold text-gray-800 mb-3">推荐好友</h2>
            <div class="bg-white rounded-2xl p-4 shadow-sm">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face" 
                             class="w-10 h-10 rounded-full mr-3">
                        <div>
                            <h4 class="font-semibold text-sm">潮流达人小王</h4>
                            <p class="text-xs text-gray-500">3个共同好友</p>
                        </div>
                    </div>
                    <button class="px-4 py-1 bg-blue-500 text-white rounded-full text-sm">关注</button>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <img src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=50&h=50&fit=crop&crop=face" 
                             class="w-10 h-10 rounded-full mr-3">
                        <div>
                            <h4 class="font-semibold text-sm">摄影师小刘</h4>
                            <p class="text-xs text-gray-500">1个共同好友</p>
                        </div>
                    </div>
                    <button class="px-4 py-1 bg-blue-500 text-white rounded-full text-sm">关注</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

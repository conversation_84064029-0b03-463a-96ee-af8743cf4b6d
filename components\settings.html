<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="bg-gray-50">
    <div class="page-content">
        <!-- 设置头部 -->
        <div class="bg-white px-4 py-3 shadow-sm">
            <div class="flex items-center">
                <i class="fas fa-arrow-left text-gray-600 mr-4"></i>
                <h1 class="text-xl font-bold text-gray-800">设置</h1>
            </div>
        </div>

        <!-- 账户设置 -->
        <div class="px-4 py-3">
            <h2 class="text-lg font-bold text-gray-800 mb-3">账户设置</h2>
            <div class="bg-white rounded-2xl shadow-sm">
                <div class="flex items-center p-4 border-b border-gray-100">
                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-user text-blue-500"></i>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-semibold text-sm">个人资料</h4>
                        <p class="text-xs text-gray-500">头像、昵称、个人信息</p>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                
                <div class="flex items-center p-4 border-b border-gray-100">
                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-shield-alt text-green-500"></i>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-semibold text-sm">账户安全</h4>
                        <p class="text-xs text-gray-500">密码、手机号、邮箱</p>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                
                <div class="flex items-center p-4">
                    <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-eye text-purple-500"></i>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-semibold text-sm">隐私设置</h4>
                        <p class="text-xs text-gray-500">谁可以看到我的信息</p>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
        </div>

        <!-- 通知设置 -->
        <div class="px-4 py-3">
            <h2 class="text-lg font-bold text-gray-800 mb-3">通知设置</h2>
            <div class="bg-white rounded-2xl shadow-sm">
                <div class="flex items-center justify-between p-4 border-b border-gray-100">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-bell text-orange-500"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-sm">推送通知</h4>
                            <p class="text-xs text-gray-500">接收消息和活动通知</p>
                        </div>
                    </div>
                    <div class="w-12 h-6 bg-blue-500 rounded-full relative">
                        <div class="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
                    </div>
                </div>
                
                <div class="flex items-center justify-between p-4 border-b border-gray-100">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-heart text-red-500"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-sm">社交通知</h4>
                            <p class="text-xs text-gray-500">点赞、评论、关注提醒</p>
                        </div>
                    </div>
                    <div class="w-12 h-6 bg-blue-500 rounded-full relative">
                        <div class="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
                    </div>
                </div>
                
                <div class="flex items-center justify-between p-4">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-shopping-cart text-yellow-500"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-sm">购物通知</h4>
                            <p class="text-xs text-gray-500">订单状态、优惠活动</p>
                        </div>
                    </div>
                    <div class="w-12 h-6 bg-gray-300 rounded-full relative">
                        <div class="w-5 h-5 bg-white rounded-full absolute left-0.5 top-0.5"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 应用设置 -->
        <div class="px-4 py-3">
            <h2 class="text-lg font-bold text-gray-800 mb-3">应用设置</h2>
            <div class="bg-white rounded-2xl shadow-sm">
                <div class="flex items-center p-4 border-b border-gray-100">
                    <div class="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-palette text-indigo-500"></i>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-semibold text-sm">主题设置</h4>
                        <p class="text-xs text-gray-500">浅色模式</p>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                
                <div class="flex items-center p-4 border-b border-gray-100">
                    <div class="w-10 h-10 bg-teal-100 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-language text-teal-500"></i>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-semibold text-sm">语言设置</h4>
                        <p class="text-xs text-gray-500">简体中文</p>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                
                <div class="flex items-center p-4 border-b border-gray-100">
                    <div class="w-10 h-10 bg-pink-100 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-download text-pink-500"></i>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-semibold text-sm">缓存管理</h4>
                        <p class="text-xs text-gray-500">已使用 128MB</p>
                    </div>
                    <span class="text-sm text-blue-500">清理</span>
                </div>
                
                <div class="flex items-center p-4">
                    <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-info-circle text-gray-500"></i>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-semibold text-sm">关于应用</h4>
                        <p class="text-xs text-gray-500">版本 1.0.0</p>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
        </div>

        <!-- 其他设置 -->
        <div class="px-4 py-3">
            <div class="bg-white rounded-2xl shadow-sm">
                <div class="flex items-center p-4 border-b border-gray-100">
                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-question-circle text-blue-500"></i>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-semibold text-sm">帮助与反馈</h4>
                        <p class="text-xs text-gray-500">常见问题、意见建议</p>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                
                <div class="flex items-center p-4 border-b border-gray-100">
                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-file-alt text-green-500"></i>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-semibold text-sm">用户协议</h4>
                        <p class="text-xs text-gray-500">服务条款和隐私政策</p>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                
                <div class="flex items-center p-4">
                    <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-sign-out-alt text-red-500"></i>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-semibold text-sm text-red-500">退出登录</h4>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8"/><meta name="viewport" content="width=device-width,initial-scale=1"/>
  <title>Product - 商品详情</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css"/>
  <style> body{font-family:Inter,system-ui;background:#fff;margin:0} .status{height:44px;display:flex;align-items:center;justify-content:space-between;padding:0 12px;color:#475569} </style>
</head>
<body class="h-screen flex flex-col">
  <div class="status"><div>9:41</div><div><i class="fa-solid fa-wifi"></i><i class="fa-solid fa-battery-full"></i></div></div>

  <main class="overflow-auto flex-1">
    <div class="p-4">
      <div class="flex items-center justify-between">
        <button class="p-2 bg-slate-100 rounded-lg"><i class="fa-solid fa-arrow-left"></i></button>
        <div class="text-sm font-semibold">商品详情</div>
        <button class="p-2 bg-slate-100 rounded-lg"><i class="fa-solid fa-share-nodes"></i></button>
      </div>

      <div class="mt-4 rounded-2xl overflow-hidden">
        <img src="https://images.unsplash.com/photo-1542291026-7eec264c27ff?q=80&w=1200&auto=format&fit=crop" class="w-full h-64 object-cover"/>
      </div>

      <div class="mt-4">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-lg font-semibold">多功能料理机 · 小型便携</div>
            <div class="text-xs text-slate-500 mt-1">品牌：家居选品</div>
          </div>
          <div class="text-rose-500 font-bold text-lg">¥299</div>
        </div>

        <div class="mt-3 flex items-center gap-3">
          <button class="px-3 py-1 bg-slate-100 rounded-full text-sm">包邮</button>
          <button class="px-3 py-1 bg-slate-100 rounded-full text-sm">30天退换</button>
        </div>

        <div class="mt-4 text-sm">
          <div class="font-semibold mb-2">商品介绍</div>
          <p class="text-slate-600">一机多用，支持果汁、搅拌、研磨。机身轻巧便携，适合上班族与学生党。</p>
        </div>

        <!-- 社交评论 -->
        <div class="mt-4">
          <div class="flex items-center justify-between mb-3">
            <div class="font-semibold">用户评论</div>
            <div class="text-xs text-slate-400">共 1.2k 条</div>
          </div>
          <div class="bg-white p-3 rounded-2xl shadow">
            <div class="flex items-start gap-3">
              <img src="https://images.unsplash.com/photo-1545996124-4f7b6b1a6b79?q=80&w=200&auto=format&fit=crop" class="w-10 h-10 rounded-full object-cover"/>
              <div class="flex-1">
                <div class="flex items-center justify-between">
                  <div class="text-sm font-semibold">用户小张</div>
                  <div class="text-xs text-slate-400">3天前</div>
                </div>
                <div class="mt-2 text-sm">用了一个月，质量不错，动力十足，早上做果汁超方便。</div>
                <div class="mt-3 flex items-center gap-4 text-sm text-slate-500">
                  <button class="flex items-center gap-2"><i class="fa-regular fa-heart"></i> 123</button>
                  <button class="flex items-center gap-2"><i class="fa-regular fa-comment"></i> 回复</button>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </main>

  <!-- 购物条 -->
  <div class="border-t p-3 bg-white flex items-center gap-3">
    <button class="p-3 bg-slate-100 rounded-lg"><i class="fa-regular fa-heart"></i></button>
    <div class="flex-1">
      <div class="text-xs text-slate-500">运费 ¥10 / 预计 2-3 天送达</div>
      <div class="font-semibold">¥299</div>
    </div>
    <a href="./cart.html" class="bg-rose-500 text-white px-4 py-2 rounded-lg">加入购物车</a>
  </div>
</body>
</html>
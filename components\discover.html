<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发现</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="bg-gray-50">
    <div class="page-content">
        <!-- 顶部导航 -->
        <div class="bg-white px-4 py-3 shadow-sm">
            <div class="flex items-center justify-between">
                <h1 class="text-xl font-bold text-gray-800">发现</h1>
                <div class="flex items-center space-x-3">
                    <i class="fas fa-search text-gray-600"></i>
                    <i class="fas fa-bell text-gray-600 relative">
                        <span class="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></span>
                    </i>
                </div>
            </div>
        </div>

        <!-- 分类标签 -->
        <div class="bg-white px-4 py-3 border-b border-gray-100">
            <div class="flex space-x-4 overflow-x-auto">
                <span class="px-4 py-2 bg-blue-500 text-white rounded-full text-sm whitespace-nowrap">推荐</span>
                <span class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap">时尚</span>
                <span class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap">数码</span>
                <span class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap">美食</span>
                <span class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap">旅行</span>
                <span class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap">生活</span>
            </div>
        </div>

        <!-- 热门话题 -->
        <div class="px-4 py-3">
            <h2 class="text-lg font-bold text-gray-800 mb-3">热门话题</h2>
            <div class="bg-white rounded-2xl p-4 shadow-sm mb-3">
                <div class="flex items-center mb-3">
                    <div class="w-8 h-8 bg-gradient-to-r from-pink-400 to-red-400 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-fire text-white text-sm"></i>
                    </div>
                    <div class="flex-1">
                        <h3 class="font-semibold text-sm">#春季穿搭指南</h3>
                        <p class="text-xs text-gray-500">12.8万人参与讨论</p>
                    </div>
                    <span class="text-xs text-blue-500">参与</span>
                </div>
                <div class="flex -space-x-2">
                    <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=30&h=30&fit=crop&crop=face" 
                         class="w-6 h-6 rounded-full border-2 border-white">
                    <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=30&h=30&fit=crop&crop=face" 
                         class="w-6 h-6 rounded-full border-2 border-white">
                    <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=30&h=30&fit=crop&crop=face" 
                         class="w-6 h-6 rounded-full border-2 border-white">
                    <div class="w-6 h-6 bg-gray-200 rounded-full border-2 border-white flex items-center justify-center">
                        <span class="text-xs text-gray-600">+</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 精选内容 -->
        <div class="px-4">
            <h2 class="text-lg font-bold text-gray-800 mb-3">精选内容</h2>
            
            <!-- 内容卡片1 -->
            <div class="bg-white rounded-2xl overflow-hidden shadow-sm mb-4">
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=200&fit=crop" 
                         alt="Content" class="w-full h-48 object-cover">
                    <div class="absolute top-3 right-3 bg-black/50 text-white px-2 py-1 rounded-full text-xs">
                        <i class="fas fa-play mr-1"></i>2:30
                    </div>
                </div>
                <div class="p-4">
                    <div class="flex items-center mb-2">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=30&h=30&fit=crop&crop=face" 
                             class="w-8 h-8 rounded-full mr-3">
                        <div class="flex-1">
                            <h4 class="font-semibold text-sm">时尚博主小雅</h4>
                            <p class="text-xs text-gray-500">1小时前</p>
                        </div>
                        <button class="text-blue-500 text-sm font-medium">关注</button>
                    </div>
                    <h3 class="font-semibold text-base mb-2">春季必备单品推荐</h3>
                    <p class="text-sm text-gray-600 mb-3">分享几件春季必备的时尚单品，让你轻松穿出时尚感～</p>
                    <div class="flex items-center justify-between text-sm text-gray-500">
                        <div class="flex items-center space-x-4">
                            <span><i class="far fa-heart mr-1"></i>1.2k</span>
                            <span><i class="far fa-comment mr-1"></i>89</span>
                            <span><i class="fas fa-share mr-1"></i>分享</span>
                        </div>
                        <span class="text-xs">#时尚穿搭</span>
                    </div>
                </div>
            </div>

            <!-- 内容卡片2 -->
            <div class="bg-white rounded-2xl overflow-hidden shadow-sm mb-4">
                <div class="grid grid-cols-3 gap-1">
                    <img src="https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=150&h=150&fit=crop" 
                         class="w-full h-24 object-cover">
                    <img src="https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=150&h=150&fit=crop" 
                         class="w-full h-24 object-cover">
                    <img src="https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=150&h=150&fit=crop" 
                         class="w-full h-24 object-cover">
                </div>
                <div class="p-4">
                    <div class="flex items-center mb-2">
                        <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=30&h=30&fit=crop&crop=face" 
                             class="w-8 h-8 rounded-full mr-3">
                        <div class="flex-1">
                            <h4 class="font-semibold text-sm">数码达人阿明</h4>
                            <p class="text-xs text-gray-500">3小时前</p>
                        </div>
                        <button class="text-gray-400 text-sm">已关注</button>
                    </div>
                    <h3 class="font-semibold text-base mb-2">我的桌面好物分享</h3>
                    <p class="text-sm text-gray-600 mb-3">整理了一下我的工作桌面，分享一些提升效率的好物～</p>
                    <div class="flex items-center justify-between text-sm text-gray-500">
                        <div class="flex items-center space-x-4">
                            <span><i class="far fa-heart mr-1"></i>856</span>
                            <span><i class="far fa-comment mr-1"></i>45</span>
                            <span><i class="fas fa-share mr-1"></i>分享</span>
                        </div>
                        <span class="text-xs">#数码好物</span>
                    </div>
                </div>
            </div>

            <!-- 内容卡片3 -->
            <div class="bg-white rounded-2xl overflow-hidden shadow-sm mb-4">
                <div class="p-4">
                    <div class="flex items-center mb-3">
                        <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=30&h=30&fit=crop&crop=face" 
                             class="w-8 h-8 rounded-full mr-3">
                        <div class="flex-1">
                            <h4 class="font-semibold text-sm">美食家小丽</h4>
                            <p class="text-xs text-gray-500">5小时前</p>
                        </div>
                        <button class="text-blue-500 text-sm font-medium">关注</button>
                    </div>
                    <h3 class="font-semibold text-base mb-2">周末在家做的简单美食</h3>
                    <p class="text-sm text-gray-600 mb-3">分享一道超简单的家常菜，新手也能轻松搞定！材料简单，味道超棒～</p>
                </div>
                <img src="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=250&fit=crop" 
                     alt="Food" class="w-full h-40 object-cover">
                <div class="p-4">
                    <div class="flex items-center justify-between text-sm text-gray-500">
                        <div class="flex items-center space-x-4">
                            <span><i class="far fa-heart mr-1"></i>2.1k</span>
                            <span><i class="far fa-comment mr-1"></i>156</span>
                            <span><i class="fas fa-share mr-1"></i>分享</span>
                        </div>
                        <span class="text-xs">#美食分享</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>Discover - Podcast Prototype</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" />
  <style> body{font-family:Inter,system-ui;background:#fff;} .status{height:44px;display:flex;align-items:center;justify-content:space-between;padding:0 12px;color:#475569;} </style>
</head>
<body>
  <div class="h-screen flex flex-col">
    <div class="status">
      <div>9:41</div>
      <div class="flex gap-2"><i class="fa-solid fa-wifi"></i><i class="fa-solid fa-battery-full"></i></div>
    </div>

    <div class="p-4">
      <div class="flex items-center gap-3">
        <div class="flex-1 bg-slate-100 rounded-xl p-3 flex items-center gap-3">
          <i class="fa-solid fa-magnifying-glass text-slate-400"></i>
          <input placeholder="搜索播客、话题或主持人" class="bg-transparent outline-none w-full text-sm" />
        </div>
        <button class="p-2"><i class="fa-solid fa-sliders"></i></button>
      </div>

      <div class="mt-4">
        <div class="text-sm font-semibold mb-2">分类</div>
        <div class="grid grid-cols-3 gap-3">
          <div class="bg-gradient-to-br from-orange-400 to-rose-400 text-white rounded-2xl p-4 flex flex-col items-start">
            <i class="fa-solid fa-microphone-lines fa-xl"></i>
            <div class="mt-3 font-semibold">娱乐</div>
            <div class="text-xs opacity-90">热门话题</div>
          </div>
          <div class="bg-gradient-to-br from-indigo-500 to-blue-500 text-white rounded-2xl p-4 flex flex-col">
            <i class="fa-solid fa-flask fa-xl"></i>
            <div class="mt-3 font-semibold">科学</div>
            <div class="text-xs opacity-90">深度内容</div>
          </div>
          <div class="bg-gradient-to-br from-emerald-400 to-teal-500 text-white rounded-2xl p-4 flex flex-col">
            <i class="fa-solid fa-plane fa-xl"></i>
            <div class="mt-3 font-semibold">旅行</div>
            <div class="text-xs opacity-90">故事与指南</div>
          </div>
        </div>
      </div>

      <div class="mt-4">
        <div class="flex items-center justify-between mb-3">
          <div class="text-sm font-semibold">排行榜</div>
          <div class="text-xs text-slate-500">实时热度</div>
        </div>
        <div class="space-y-3">
          <div class="flex items-center gap-3 bg-white p-3 rounded-2xl shadow">
            <div class="w-16 h-16 rounded-lg overflow-hidden"><img src="https://images.unsplash.com/photo-1512428559087-560fa5ceab42?q=80&w=800&auto=format&fit=crop" class="w-full h-full object-cover"/></div>
            <div class="flex-1">
              <div class="font-semibold">每日新闻快讯</div>
              <div class="text-xs text-slate-500">10.2k 播放 · 时政</div>
            </div>
            <div class="text-indigo-600 font-bold">#1</div>
          </div>

          <div class="flex items-center gap-3 bg-white p-3 rounded-2xl shadow">
            <div class="w-16 h-16 rounded-lg overflow-hidden"><img src="https://images.unsplash.com/photo-1515879218367-8466d910aaa4?q=80&w=800&auto=format&fit=crop" class="w-full h-full object-cover"/></div>
            <div class="flex-1">
              <div class="font-semibold">女性故事会</div>
              <div class="text-xs text-slate-500">8.6k 播放 · 亲子</div>
            </div>
            <div class="text-indigo-600 font-bold">#2</div>
          </div>
        </div>
      </div>
    </div>

    <!-- bottom tab -->
    <div class="border-t p-3">
      <div class="flex justify-around text-slate-600">
        <div class="flex flex-col items-center"><i class="fa-solid fa-house"></i><div class="text-xs">首页</div></div>
        <div class="flex flex-col items-center text-indigo-600"><i class="fa-solid fa-compass"></i><div class="text-xs">发现</div></div>
        <div class="flex flex-col items-center"><i class="fa-solid fa-play-circle"></i><div class="text-xs">播放</div></div>
        <div class="flex flex-col items-center"><i class="fa-solid fa-user"></i><div class="text-xs">我的</div></div>
      </div>
    </div>
  </div>
</body>
</html>
<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8"/><meta name="viewport" content="width=device-width,initial-scale=1"/>
  <title>Cart - 购物车</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css"/>
  <style> body{font-family:Inter,system-ui;background:#f8fafc;margin:0} .status{height:44px;display:flex;align-items:center;justify-content:space-between;padding:0 12px;color:#475569} </style>
</head>
<body class="h-screen flex flex-col">
  <div class="status"><div>9:41</div><div><i class="fa-solid fa-wifi"></i><i class="fa-solid fa-battery-full"></i></div></div>

  <main class="p-4 overflow-auto flex-1">
    <div class="flex items-center justify-between mb-4">
      <div class="text-lg font-semibold">购物车</div>
      <div class="text-sm text-slate-400">编辑</div>
    </div>

    <div class="space-y-3">
      <div class="bg-white p-3 rounded-2xl shadow flex items-center gap-3">
        <input type="checkbox" checked class="w-5 h-5"/>
        <img src="https://images.unsplash.com/photo-1542291026-7eec264c27ff?q=80&w=800&auto=format&fit=crop" class="w-20 h-20 object-cover rounded-lg"/>
        <div class="flex-1">
          <div class="font-medium">多功能料理机</div>
          <div class="text-xs text-slate-400 mt-1">¥299</div>
          <div class="mt-2 flex items-center gap-2">
            <button class="px-3 py-1 bg-slate-100 rounded">-</button>
            <div class="px-3">1</div>
            <button class="px-3 py-1 bg-slate-100 rounded">+</button>
          </div>
        </div>
      </div>

      <div class="bg-white p-3 rounded-2xl shadow">
        <div class="flex items-center justify-between">
          <div class="text-sm">优惠券</div>
          <div class="text-sm text-indigo-600">选择优惠</div>
        </div>
      </div>
    </div>
  </main>

  <footer class="p-3 bg-white border-t">
    <div class="flex items-center justify-between mb-2">
      <div class="flex items-center gap-2"><input type="checkbox" checked class="w-5 h-5"/> <div class="text-sm">全选</div></div>
      <div class="text-sm">合计：<span class="font-bold text-rose-500">¥299</span></div>
    </div>
    <div class="flex gap-3">
      <button class="flex-1 bg-slate-100 text-sm py-3 rounded-lg">继续购物</button>
      <button class="flex-1 bg-rose-500 text-white text-sm py-3 rounded-lg">去结算</button>
    </div>
  </footer>
</body>
</html>
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./components/**/*.{html,js,vue}",
    "./js/**/*.js"
  ],
  theme: {
    extend: {
      colors: {
        'ios-blue': '#007AFF',
        'ios-gray': '#F2F2F7',
        'ios-dark': '#1C1C1E',
        'ios-light': '#FFFFFF',
        'ios-red': '#FF3B30',
        'ios-green': '#34C759',
        'ios-orange': '#FF9500',
        'ios-purple': '#AF52DE'
      },
      fontFamily: {
        'sf': ['-apple-system', 'BlinkMacSystemFont', 'SF Pro Display', 'Segoe UI', 'Roboto', 'sans-serif']
      },
      screens: {
        'iphone': '393px'
      },
      spacing: {
        'safe-top': '44px',
        'safe-bottom': '34px',
        'tab-bar': '83px'
      },
      borderRadius: {
        'ios': '10px',
        'ios-lg': '16px',
        'iphone': '40px'
      },
      boxShadow: {
        'ios': '0 1px 3px rgba(0, 0, 0, 0.1)',
        'ios-card': '0 2px 10px rgba(0, 0, 0, 0.1)'
      }
    },
  },
  plugins: [
    require('@tailwindcss/forms')
  ],
}

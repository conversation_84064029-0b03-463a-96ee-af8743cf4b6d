<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8"/>
  <meta name="viewport" content="width=device-width,initial-scale=1"/>
  <title>Home - 社交购物商城</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css"/>
  <style>
    body{font-family:Inter,system-ui;background:#f8fafc;color:#0b1220;margin:0}
    .status{height:44px;display:flex;align-items:center;justify-content:space-between;padding:0 12px;color:#475569}
    .tab {height:70px;border-top:1px solid #eef2f7;padding:10px;}
  </style>
</head>
<body class="h-screen flex flex-col">
  <!-- iOS 状态栏 -->
  <div class="status">
    <div>9:41</div>
    <div class="flex items-center gap-3 text-sm"><i class="fa-solid fa-wifi"></i><i class="fa-solid fa-battery-full"></i></div>
  </div>

  <!-- 顶部 Nav -->
  <header class="p-4 flex items-center justify-between">
    <div class="flex items-center gap-3">
      <img src="https://images.unsplash.com/photo-1544005313-94ddf0286df2?q=80&w=200&auto=format&fit=crop" class="w-10 h-10 rounded-full object-cover"/>
      <div>
        <div class="text-sm font-semibold">早上好，用户</div>
        <div class="text-xs text-slate-500">为你推荐好货</div>
      </div>
    </div>
    <div class="flex items-center gap-3">
      <button class="p-2 bg-white rounded-lg shadow"><i class="fa-solid fa-search"></i></button>
      <button class="p-2 bg-white rounded-lg shadow"><i class="fa-solid fa-bell"></i></button>
    </div>
  </header>

  <!-- 内容流 -->
  <main class="p-4 overflow-auto flex-1">
    <!-- Stories / Promo -->
    <div class="flex gap-3 overflow-x-auto pb-3">
      <div class="min-w-[220px] rounded-2xl overflow-hidden relative">
        <img src="https://images.unsplash.com/photo-1523275335684-37898b6baf30?q=80&w=1200&auto=format&fit=crop" class="w-full h-40 object-cover"/>
        <div class="absolute bottom-3 left-3 text-white font-semibold">限时0元试用·新客礼</div>
      </div>
      <div class="min-w-[220px] rounded-2xl overflow-hidden relative">
        <img src="https://images.unsplash.com/photo-1526170375885-4d8ecf77b99f?q=80&w=1200&auto=format&fit=crop" class="w-full h-40 object-cover"/>
        <div class="absolute bottom-3 left-3 text-white font-semibold">今日爆款</div>
      </div>
    </div>

    <!-- 社交内容卡 -->
    <section class="mt-4 space-y-4">
      <div class="bg-white rounded-2xl p-4 shadow">
        <div class="flex items-center gap-3">
          <img src="https://images.unsplash.com/photo-1545996124-4f7b6b1a6b79?q=80&w=200&auto=format&fit=crop" class="w-10 h-10 rounded-full object-cover"/>
          <div>
            <div class="text-sm font-semibold">达人小李</div>
            <div class="text-xs text-slate-400">2小时前 · 关注 12.3k</div>
          </div>
          <div class="ml-auto text-slate-400"><i class="fa-solid fa-ellipsis"></i></div>
        </div>
        <div class="mt-3 text-sm">这款多功能料理机，做早餐只需三分钟，还附赠配方~</div>
        <div class="mt-3 grid grid-cols-2 gap-2">
          <img src="https://images.unsplash.com/photo-1543353071-087092ec3933?q=80&w=800&auto=format&fit=crop" class="w-full h-32 object-cover rounded-lg"/>
          <img src="https://images.unsplash.com/photo-1511690743698-d9d85f2fbf38?q=80&w=800&auto=format&fit=crop" class="w-full h-32 object-cover rounded-lg"/>
        </div>
        <div class="mt-3 flex items-center justify-between text-sm text-slate-500">
          <div class="flex items-center gap-4">
            <button class="flex items-center gap-2"><i class="fa-regular fa-heart"></i> 1.2k</button>
            <button class="flex items-center gap-2"><i class="fa-regular fa-comment"></i> 256</button>
          </div>
          <div class="flex items-center gap-3">
            <button class="px-3 py-1 bg-indigo-600 text-white rounded-lg text-sm"><i class="fa-solid fa-cart-plus"></i> 加入购物车</button>
            <a href="./product.html" class="text-indigo-600 text-sm">查看商品</a>
          </div>
        </div>
      </div>

      <!-- 商品推荐列表 -->
      <div>
        <div class="flex items-center justify-between mb-3">
          <div class="text-sm font-semibold">为你精选</div>
          <div class="text-xs text-slate-400">更多</div>
        </div>
        <div class="space-y-3">
          <article class="bg-white p-3 rounded-2xl flex gap-3 shadow">
            <img src="https://images.unsplash.com/photo-1580910051071-4e1f3a7e7d7b?q=80&w=800&auto=format&fit=crop" class="w-24 h-24 object-cover rounded-lg"/>
            <div class="flex-1">
              <div class="font-semibold text-sm">北欧风多功能料理机</div>
              <div class="text-xs text-slate-500 mt-1">¥299 · 评分 4.8</div>
              <div class="mt-3 flex items-center gap-2">
                <button class="text-indigo-600 text-sm"><i class="fa-solid fa-share-nodes"></i> 分享</button>
                <button class="text-sm text-slate-400"><i class="fa-regular fa-heart"></i> 收藏</button>
              </div>
            </div>
            <div class="flex flex-col items-end justify-between">
              <div class="text-sm font-bold text-rose-500">¥299</div>
              <button class="bg-indigo-600 text-white px-3 py-1 rounded-lg"><i class="fa-solid fa-cart-plus"></i></button>
            </div>
          </article>
        </div>
      </div>
    </section>
  </main>

  <!-- 底部 Tab -->
  <nav class="tab bg-white">
    <div class="flex justify-between items-center px-6 text-slate-600">
      <div class="flex flex-col items-center"><i class="fa-solid fa-house"></i><span class="text-xs">首页</span></div>
      <div class="flex flex-col items-center"><i class="fa-solid fa-list"></i><span class="text-xs">分类</span></div>
      <div class="flex flex-col items-center text-indigo-600"><i class="fa-solid fa-shopping-cart"></i><span class="text-xs">购物车</span></div>
      <div class="flex flex-col items-center"><i class="fa-solid fa-message"></i><span class="text-xs">消息</span></div>
      <div class="flex flex-col items-center"><i class="fa-solid fa-user"></i><span class="text-xs">我的</span></div>
    </div>
  </nav>
</body>
</html>
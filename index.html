<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>社交购物商城APP - 高保真原型</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
        }
        
        .prototype-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .prototype-title {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .phones-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            justify-items: center;
        }
        
        .phone-wrapper {
            text-align: center;
        }
        
        .phone-label {
            color: white;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .page-iframe {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 32px;
            background: white;
        }
        
        .phone-screen {
            position: relative;
            overflow: hidden;
        }
        
        .phone-screen iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body>
    <div class="prototype-container">
        <!-- 标题 -->
        <div class="prototype-title">
            <h1 class="text-4xl font-bold mb-4">社交购物商城APP</h1>
            <p class="text-xl opacity-90">高保真原型设计 - iPhone 15 Pro 展示</p>
            <p class="text-sm opacity-75 mt-2">所有界面均采用现代化iOS设计规范，支持真实交互体验</p>
        </div>

        <!-- 手机展示网格 -->
        <div class="phones-grid">
            <!-- 首页 -->
            <div class="phone-wrapper">
                <div class="phone-label">首页 - Home</div>
                <div class="iphone-container">
                    <div class="iphone-screen">
                        <!-- iOS状态栏 -->
                        <div class="status-bar">
                            <div class="flex items-center">
                                <span class="status-time">9:41</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-signal text-sm"></i>
                                <i class="fas fa-wifi text-sm"></i>
                                <div class="w-6 h-3 border border-white rounded-sm">
                                    <div class="w-4 h-1 bg-white rounded-sm m-0.5"></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 页面内容 -->
                        <iframe id="home-frame" class="page-iframe" src="components/home.html"></iframe>
                        
                        <!-- Tab Bar -->
                        <div class="tab-bar">
                            <div class="tab-item active" data-tab="home">
                                <i class="fas fa-home"></i>
                                <span>首页</span>
                            </div>
                            <div class="tab-item" data-tab="discover">
                                <i class="fas fa-compass"></i>
                                <span>发现</span>
                            </div>
                            <div class="tab-item" data-tab="social">
                                <i class="fas fa-comments"></i>
                                <span>消息</span>
                            </div>
                            <div class="tab-item" data-tab="cart">
                                <i class="fas fa-shopping-cart"></i>
                                <span>购物车</span>
                            </div>
                            <div class="tab-item" data-tab="profile">
                                <i class="fas fa-user"></i>
                                <span>我的</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 发现页 -->
            <div class="phone-wrapper">
                <div class="phone-label">发现页 - Discover</div>
                <div class="iphone-container">
                    <div class="iphone-screen">
                        <div class="status-bar">
                            <div class="flex items-center">
                                <span class="status-time">9:41</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-signal text-sm"></i>
                                <i class="fas fa-wifi text-sm"></i>
                                <div class="w-6 h-3 border border-white rounded-sm">
                                    <div class="w-4 h-1 bg-white rounded-sm m-0.5"></div>
                                </div>
                            </div>
                        </div>
                        <iframe id="discover-frame" class="page-iframe" src="components/discover.html" style="display: none;"></iframe>
                        <div class="tab-bar">
                            <div class="tab-item" data-tab="home">
                                <i class="fas fa-home"></i>
                                <span>首页</span>
                            </div>
                            <div class="tab-item active" data-tab="discover">
                                <i class="fas fa-compass"></i>
                                <span>发现</span>
                            </div>
                            <div class="tab-item" data-tab="social">
                                <i class="fas fa-comments"></i>
                                <span>消息</span>
                            </div>
                            <div class="tab-item" data-tab="cart">
                                <i class="fas fa-shopping-cart"></i>
                                <span>购物车</span>
                            </div>
                            <div class="tab-item" data-tab="profile">
                                <i class="fas fa-user"></i>
                                <span>我的</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 社交页 -->
            <div class="phone-wrapper">
                <div class="phone-label">社交消息 - Social</div>
                <div class="iphone-container">
                    <div class="iphone-screen">
                        <div class="status-bar">
                            <div class="flex items-center">
                                <span class="status-time">9:41</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-signal text-sm"></i>
                                <i class="fas fa-wifi text-sm"></i>
                                <div class="w-6 h-3 border border-white rounded-sm">
                                    <div class="w-4 h-1 bg-white rounded-sm m-0.5"></div>
                                </div>
                            </div>
                        </div>
                        <iframe id="social-frame" class="page-iframe" src="components/social.html" style="display: none;"></iframe>
                        <div class="tab-bar">
                            <div class="tab-item" data-tab="home">
                                <i class="fas fa-home"></i>
                                <span>首页</span>
                            </div>
                            <div class="tab-item" data-tab="discover">
                                <i class="fas fa-compass"></i>
                                <span>发现</span>
                            </div>
                            <div class="tab-item active" data-tab="social">
                                <i class="fas fa-comments"></i>
                                <span>消息</span>
                            </div>
                            <div class="tab-item" data-tab="cart">
                                <i class="fas fa-shopping-cart"></i>
                                <span>购物车</span>
                            </div>
                            <div class="tab-item" data-tab="profile">
                                <i class="fas fa-user"></i>
                                <span>我的</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 购物车页 -->
            <div class="phone-wrapper">
                <div class="phone-label">购物车 - Cart</div>
                <div class="iphone-container">
                    <div class="iphone-screen">
                        <div class="status-bar">
                            <div class="flex items-center">
                                <span class="status-time">9:41</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-signal text-sm"></i>
                                <i class="fas fa-wifi text-sm"></i>
                                <div class="w-6 h-3 border border-white rounded-sm">
                                    <div class="w-4 h-1 bg-white rounded-sm m-0.5"></div>
                                </div>
                            </div>
                        </div>
                        <iframe id="cart-frame" class="page-iframe" src="components/cart.html" style="display: none;"></iframe>
                        <div class="tab-bar">
                            <div class="tab-item" data-tab="home">
                                <i class="fas fa-home"></i>
                                <span>首页</span>
                            </div>
                            <div class="tab-item" data-tab="discover">
                                <i class="fas fa-compass"></i>
                                <span>发现</span>
                            </div>
                            <div class="tab-item" data-tab="social">
                                <i class="fas fa-comments"></i>
                                <span>消息</span>
                            </div>
                            <div class="tab-item active" data-tab="cart">
                                <i class="fas fa-shopping-cart"></i>
                                <span>购物车</span>
                            </div>
                            <div class="tab-item" data-tab="profile">
                                <i class="fas fa-user"></i>
                                <span>我的</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 个人中心页 -->
            <div class="phone-wrapper">
                <div class="phone-label">个人中心 - Profile</div>
                <div class="iphone-container">
                    <div class="iphone-screen">
                        <div class="status-bar">
                            <div class="flex items-center">
                                <span class="status-time">9:41</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-signal text-sm"></i>
                                <i class="fas fa-wifi text-sm"></i>
                                <div class="w-6 h-3 border border-white rounded-sm">
                                    <div class="w-4 h-1 bg-white rounded-sm m-0.5"></div>
                                </div>
                            </div>
                        </div>
                        <iframe id="profile-frame" class="page-iframe" src="components/profile.html" style="display: none;"></iframe>
                        <div class="tab-bar">
                            <div class="tab-item" data-tab="home">
                                <i class="fas fa-home"></i>
                                <span>首页</span>
                            </div>
                            <div class="tab-item" data-tab="discover">
                                <i class="fas fa-compass"></i>
                                <span>发现</span>
                            </div>
                            <div class="tab-item" data-tab="social">
                                <i class="fas fa-comments"></i>
                                <span>消息</span>
                            </div>
                            <div class="tab-item" data-tab="cart">
                                <i class="fas fa-shopping-cart"></i>
                                <span>购物车</span>
                            </div>
                            <div class="tab-item active" data-tab="profile">
                                <i class="fas fa-user"></i>
                                <span>我的</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 商品详情页 -->
            <div class="phone-wrapper">
                <div class="phone-label">商品详情 - Product Detail</div>
                <div class="iphone-container">
                    <div class="iphone-screen">
                        <div class="status-bar">
                            <div class="flex items-center">
                                <span class="status-time">9:41</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-signal text-sm"></i>
                                <i class="fas fa-wifi text-sm"></i>
                                <div class="w-6 h-3 border border-white rounded-sm">
                                    <div class="w-4 h-1 bg-white rounded-sm m-0.5"></div>
                                </div>
                            </div>
                        </div>
                        <iframe class="page-iframe" src="components/product-detail.html"></iframe>
                    </div>
                </div>
            </div>

            <!-- 聊天详情页 -->
            <div class="phone-wrapper">
                <div class="phone-label">聊天详情 - Chat Detail</div>
                <div class="iphone-container">
                    <div class="iphone-screen">
                        <div class="status-bar">
                            <div class="flex items-center">
                                <span class="status-time">9:41</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-signal text-sm"></i>
                                <i class="fas fa-wifi text-sm"></i>
                                <div class="w-6 h-3 border border-white rounded-sm">
                                    <div class="w-4 h-1 bg-white rounded-sm m-0.5"></div>
                                </div>
                            </div>
                        </div>
                        <iframe class="page-iframe" src="components/chat-detail.html"></iframe>
                    </div>
                </div>
            </div>

            <!-- 搜索页 -->
            <div class="phone-wrapper">
                <div class="phone-label">搜索页 - Search</div>
                <div class="iphone-container">
                    <div class="iphone-screen">
                        <div class="status-bar">
                            <div class="flex items-center">
                                <span class="status-time">9:41</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-signal text-sm"></i>
                                <i class="fas fa-wifi text-sm"></i>
                                <div class="w-6 h-3 border border-white rounded-sm">
                                    <div class="w-4 h-1 bg-white rounded-sm m-0.5"></div>
                                </div>
                            </div>
                        </div>
                        <iframe class="page-iframe" src="components/search.html"></iframe>
                    </div>
                </div>
            </div>

            <!-- 设置页 -->
            <div class="phone-wrapper">
                <div class="phone-label">设置页 - Settings</div>
                <div class="iphone-container">
                    <div class="iphone-screen">
                        <div class="status-bar">
                            <div class="flex items-center">
                                <span class="status-time">9:41</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-signal text-sm"></i>
                                <i class="fas fa-wifi text-sm"></i>
                                <div class="w-6 h-3 border border-white rounded-sm">
                                    <div class="w-4 h-1 bg-white rounded-sm m-0.5"></div>
                                </div>
                            </div>
                        </div>
                        <iframe class="page-iframe" src="components/settings.html"></iframe>
                    </div>
                </div>
            </div>

            <!-- 结算页 -->
            <div class="phone-wrapper">
                <div class="phone-label">结算页 - Checkout</div>
                <div class="iphone-container">
                    <div class="iphone-screen">
                        <div class="status-bar">
                            <div class="flex items-center">
                                <span class="status-time">9:41</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-signal text-sm"></i>
                                <i class="fas fa-wifi text-sm"></i>
                                <div class="w-6 h-3 border border-white rounded-sm">
                                    <div class="w-4 h-1 bg-white rounded-sm m-0.5"></div>
                                </div>
                            </div>
                        </div>
                        <iframe class="page-iframe" src="components/checkout.html"></iframe>
                    </div>
                </div>
            </div>

            <!-- 我的订单页 -->
            <div class="phone-wrapper">
                <div class="phone-label">我的订单 - My Orders</div>
                <div class="iphone-container">
                    <div class="iphone-screen">
                        <div class="status-bar">
                            <div class="flex items-center">
                                <span class="status-time">9:41</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-signal text-sm"></i>
                                <i class="fas fa-wifi text-sm"></i>
                                <div class="w-6 h-3 border border-white rounded-sm">
                                    <div class="w-4 h-1 bg-white rounded-sm m-0.5"></div>
                                </div>
                            </div>
                        </div>
                        <iframe class="page-iframe" src="components/my-orders.html"></iframe>
                    </div>
                </div>
            </div>

            <!-- 收货地址页 -->
            <div class="phone-wrapper">
                <div class="phone-label">收货地址 - Address</div>
                <div class="iphone-container">
                    <div class="iphone-screen">
                        <div class="status-bar">
                            <div class="flex items-center">
                                <span class="status-time">9:41</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-signal text-sm"></i>
                                <i class="fas fa-wifi text-sm"></i>
                                <div class="w-6 h-3 border border-white rounded-sm">
                                    <div class="w-4 h-1 bg-white rounded-sm m-0.5"></div>
                                </div>
                            </div>
                        </div>
                        <iframe class="page-iframe" src="components/address.html"></iframe>
                    </div>
                </div>
            </div>

            <!-- 优惠券页 -->
            <div class="phone-wrapper">
                <div class="phone-label">我的优惠券 - Coupons</div>
                <div class="iphone-container">
                    <div class="iphone-screen">
                        <div class="status-bar">
                            <div class="flex items-center">
                                <span class="status-time">9:41</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-signal text-sm"></i>
                                <i class="fas fa-wifi text-sm"></i>
                                <div class="w-6 h-3 border border-white rounded-sm">
                                    <div class="w-4 h-1 bg-white rounded-sm m-0.5"></div>
                                </div>
                            </div>
                        </div>
                        <iframe class="page-iframe" src="components/coupons.html"></iframe>
                    </div>
                </div>
            </div>
        </div>

        <!-- 项目说明 -->
        <div class="text-center mt-12 text-white">
            <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 max-w-4xl mx-auto">
                <h2 class="text-2xl font-bold mb-4">项目特色</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
                    <div>
                        <i class="fas fa-mobile-alt text-2xl mb-2"></i>
                        <h3 class="font-semibold mb-2">iPhone 15 Pro 设计</h3>
                        <p class="opacity-90">完全按照 iPhone 15 Pro 尺寸 (393x852px) 设计，包含真实的状态栏和圆角效果</p>
                    </div>
                    <div>
                        <i class="fas fa-palette text-2xl mb-2"></i>
                        <h3 class="font-semibold mb-2">iOS 设计规范</h3>
                        <p class="opacity-90">采用苹果官方设计语言，包含 SF Pro 字体、iOS 色彩系统和交互规范</p>
                    </div>
                    <div>
                        <i class="fas fa-users text-2xl mb-2"></i>
                        <h3 class="font-semibold mb-2">社交购物融合</h3>
                        <p class="opacity-90">创新性地将社交功能与购物体验结合，提供完整的用户交互流程</p>
                    </div>
                </div>
                <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
                    <div class="bg-white/10 rounded-lg p-3">
                        <h4 class="font-semibold mb-2">📱 13个完整界面</h4>
                        <p class="opacity-90">首页、发现、社交、购物车、个人中心、商品详情、聊天详情、搜索、设置、结算、订单、地址、优惠券</p>
                    </div>
                    <div class="bg-white/10 rounded-lg p-3">
                        <h4 class="font-semibold mb-2">🛍️ 完整购物流程</h4>
                        <p class="opacity-90">从商品浏览到下单结算，包含地址管理、优惠券使用、订单跟踪等完整功能</p>
                    </div>
                </div>
                <div class="mt-4 text-xs opacity-75">
                    <p>本原型使用 Vue 3 + Tailwind CSS + Font Awesome 构建，所有图片来源于 Unsplash</p>
                    <p>适用于产品设计、开发参考和用户体验测试，可直接用于实际开发</p>
                </div>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
    <script>
        // 初始化时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', { 
                hour: '2-digit', 
                minute: '2-digit',
                hour12: false 
            });
            document.querySelectorAll('.status-time').forEach(el => {
                el.textContent = timeString;
            });
        }
        
        // 每分钟更新时间
        updateTime();
        setInterval(updateTime, 60000);
        
        // 显示发现页内容
        setTimeout(() => {
            document.getElementById('discover-frame').style.display = 'block';
        }, 500);
        
        // 显示社交页内容
        setTimeout(() => {
            document.getElementById('social-frame').style.display = 'block';
        }, 1000);
        
        // 显示购物车页内容
        setTimeout(() => {
            document.getElementById('cart-frame').style.display = 'block';
        }, 1500);
        
        // 显示个人中心页内容
        setTimeout(() => {
            document.getElementById('profile-frame').style.display = 'block';
        }, 2000);
    </script>
</body>
</html>

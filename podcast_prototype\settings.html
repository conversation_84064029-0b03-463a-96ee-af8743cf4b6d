<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>Settings - Podcast Prototype</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" />
  <style> body{font-family:Inter,system-ui;background:#fff;} .status{height:44px;display:flex;align-items:center;justify-content:space-between;padding:0 12px;color:#475569;} </style>
</head>
<body>
  <div class="h-screen">
    <div class="status"><div>9:41</div><div><i class="fa-solid fa-battery-full"></i></div></div>

    <div class="p-4">
      <div class="text-lg font-semibold mb-4">设置</div>

      <div class="space-y-3">
        <div class="bg-white rounded-2xl p-3 shadow flex items-center justify-between">
          <div class="flex items-center gap-3">
            <i class="fa-solid fa-user text-indigo-600"></i>
            <div>
              <div class="font-medium">账户</div>
              <div class="text-xs text-slate-500">登录与隐私</div>
            </div>
          </div>
          <i class="fa-solid fa-chevron-right text-slate-400"></i>
        </div>

        <div class="bg-white rounded-2xl p-3 shadow flex items-center justify-between">
          <div class="flex items-center gap-3">
            <i class="fa-solid fa-bell text-orange-500"></i>
            <div>
              <div class="font-medium">通知</div>
              <div class="text-xs text-slate-500">推送设置</div>
            </div>
          </div>
          <label class="inline-flex relative items-center cursor-pointer">
            <input type="checkbox" class="sr-only peer" checked>
            <div class="w-11 h-6 bg-gray-200 peer-checked:bg-indigo-600 rounded-full"></div>
          </label>
        </div>

        <div class="bg-white rounded-2xl p-3 shadow flex items-center justify-between">
          <div class="flex items-center gap-3">
            <i class="fa-solid fa-circle-info text-emerald-500"></i>
            <div>
              <div class="font-medium">关于</div>
              <div class="text-xs text-slate-500">版本、帮助</div>
            </div>
          </div>
          <i class="fa-solid fa-chevron-right text-slate-400"></i>
        </div>
      </div>

      <div class="mt-6 text-xs text-slate-500">更多</div>
      <div class="mt-2 space-y-2">
        <div class="text-sm text-indigo-600">退出登录</div>
        <div class="text-sm text-slate-500">清除缓存</div>
      </div>
    </div>

    <div class="border-t p-3 text-xs text-slate-400 text-center">© 2025 Podcast Prototype</div>
  </div>
</body>
</html>
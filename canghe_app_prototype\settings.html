<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8"/><meta name="viewport" content="width=device-width,initial-scale=1"/>
  <title>Settings - 设置</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css"/>
  <style> body{font-family:Inter,system-ui;background:#fff;margin:0} .status{height:44px;display:flex;align-items:center;justify-content:space-between;padding:0 12px;color:#475569} </style>
</head>
<body class="h-screen flex flex-col">
  <div class="status"><div>9:41</div><div><i class="fa-solid fa-battery-full"></i></div></div>

  <main class="p-4 overflow-auto flex-1">
    <div class="text-lg font-semibold mb-4">设置</div>

    <div class="space-y-3">
      <div class="bg-white p-3 rounded-2xl shadow flex items-center justify-between">
        <div class="flex items-center gap-3">
          <i class="fa-solid fa-user text-indigo-600"></i>
          <div>
            <div class="font-medium">账号与安全</div>
            <div class="text-xs text-slate-400">登录、绑定</div>
          </div>
        </div>
        <i class="fa-solid fa-chevron-right text-slate-400"></i>
      </div>

      <div class="bg-white p-3 rounded-2xl shadow flex items-center justify-between">
        <div class="flex items-center gap-3">
          <i class="fa-solid fa-bell text-orange-500"></i>
          <div>
            <div class="font-medium">通知</div>
            <div class="text-xs text-slate-400">消息与推送</div>
          </div>
        </div>
        <label class="inline-flex relative items-center cursor-pointer">
          <input type="checkbox" class="sr-only peer" checked>
          <div class="w-11 h-6 bg-gray-200 peer-checked:bg-indigo-600 rounded-full"></div>
        </label>
      </div>

      <div class="bg-white p-3 rounded-2xl shadow flex items-center justify-between">
        <div class="flex items-center gap-3">
          <i class="fa-solid fa-circle-info text-emerald-500"></i>
          <div>
            <div class="font-medium">关于我们</div>
            <div class="text-xs text-slate-400">版本与协议</div>
          </div>
        </div>
        <i class="fa-solid fa-chevron-right text-slate-400"></i>
      </div>
    </div>

    <div class="mt-6 text-xs text-slate-500">更多操作</div>
    <div class="mt-3 space-y-2">
      <button class="w-full bg-rose-500 text-white py-3 rounded-lg">退出登录</button>
      <button class="w-full bg-slate-100 py-3 rounded-lg">清除缓存</button>
    </div>
  </main>

  <footer class="border-t p-3 text-center text-xs text-slate-400">© 2025 社交购物商城 原型</footer>
</body>
</html>
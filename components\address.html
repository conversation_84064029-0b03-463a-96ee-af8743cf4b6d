<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收货地址</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="bg-gray-50">
    <div class="page-content">
        <!-- 地址头部 -->
        <div class="bg-white px-4 py-3 shadow-sm">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <i class="fas fa-arrow-left text-gray-600 mr-4"></i>
                    <h1 class="text-xl font-bold text-gray-800">收货地址</h1>
                </div>
                <span class="text-blue-500 text-sm">管理</span>
            </div>
        </div>

        <!-- 地址列表 -->
        <div class="px-4 py-3 space-y-3">
            <!-- 默认地址 -->
            <div class="bg-white rounded-2xl p-4 shadow-sm border-2 border-blue-500">
                <div class="flex items-start justify-between mb-3">
                    <div class="flex-1">
                        <div class="flex items-center mb-2">
                            <span class="font-semibold text-base mr-3">张小明</span>
                            <span class="text-gray-600">138****8888</span>
                            <span class="bg-red-500 text-white px-2 py-1 rounded text-xs ml-2">默认</span>
                        </div>
                        <p class="text-gray-700 text-sm leading-relaxed">
                            北京市朝阳区三里屯街道工体北路8号院1号楼2单元301室
                        </p>
                    </div>
                    <i class="fas fa-edit text-gray-400 ml-3"></i>
                </div>
                
                <div class="flex items-center justify-between pt-3 border-t border-gray-100">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-blue-500 mr-2"></i>
                        <span class="text-sm text-blue-500">默认地址</span>
                    </div>
                    <div class="flex items-center space-x-3 text-sm text-gray-500">
                        <span>编辑</span>
                        <span>删除</span>
                    </div>
                </div>
            </div>

            <!-- 地址2 -->
            <div class="bg-white rounded-2xl p-4 shadow-sm">
                <div class="flex items-start justify-between mb-3">
                    <div class="flex-1">
                        <div class="flex items-center mb-2">
                            <span class="font-semibold text-base mr-3">张小明</span>
                            <span class="text-gray-600">138****8888</span>
                        </div>
                        <p class="text-gray-700 text-sm leading-relaxed">
                            上海市浦东新区陆家嘴街道世纪大道100号环球金融中心A座2008室
                        </p>
                    </div>
                    <i class="fas fa-edit text-gray-400 ml-3"></i>
                </div>
                
                <div class="flex items-center justify-between pt-3 border-t border-gray-100">
                    <div class="flex items-center">
                        <i class="far fa-circle text-gray-400 mr-2"></i>
                        <span class="text-sm text-gray-500">设为默认</span>
                    </div>
                    <div class="flex items-center space-x-3 text-sm text-gray-500">
                        <span>编辑</span>
                        <span>删除</span>
                    </div>
                </div>
            </div>

            <!-- 地址3 -->
            <div class="bg-white rounded-2xl p-4 shadow-sm">
                <div class="flex items-start justify-between mb-3">
                    <div class="flex-1">
                        <div class="flex items-center mb-2">
                            <span class="font-semibold text-base mr-3">李小美</span>
                            <span class="text-gray-600">159****6666</span>
                        </div>
                        <p class="text-gray-700 text-sm leading-relaxed">
                            广东省深圳市南山区科技园南区深南大道9988号腾讯大厦35楼
                        </p>
                    </div>
                    <i class="fas fa-edit text-gray-400 ml-3"></i>
                </div>
                
                <div class="flex items-center justify-between pt-3 border-t border-gray-100">
                    <div class="flex items-center">
                        <i class="far fa-circle text-gray-400 mr-2"></i>
                        <span class="text-sm text-gray-500">设为默认</span>
                    </div>
                    <div class="flex items-center space-x-3 text-sm text-gray-500">
                        <span>编辑</span>
                        <span>删除</span>
                    </div>
                </div>
            </div>

            <!-- 地址4 -->
            <div class="bg-white rounded-2xl p-4 shadow-sm">
                <div class="flex items-start justify-between mb-3">
                    <div class="flex-1">
                        <div class="flex items-center mb-2">
                            <span class="font-semibold text-base mr-3">王大明</span>
                            <span class="text-gray-600">186****9999</span>
                            <span class="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs ml-2">家</span>
                        </div>
                        <p class="text-gray-700 text-sm leading-relaxed">
                            浙江省杭州市西湖区文三路269号华星时代广场B座1501室
                        </p>
                    </div>
                    <i class="fas fa-edit text-gray-400 ml-3"></i>
                </div>
                
                <div class="flex items-center justify-between pt-3 border-t border-gray-100">
                    <div class="flex items-center">
                        <i class="far fa-circle text-gray-400 mr-2"></i>
                        <span class="text-sm text-gray-500">设为默认</span>
                    </div>
                    <div class="flex items-center space-x-3 text-sm text-gray-500">
                        <span>编辑</span>
                        <span>删除</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加地址提示 -->
        <div class="px-4 py-6">
            <div class="bg-blue-50 rounded-2xl p-4 text-center">
                <i class="fas fa-map-marker-alt text-blue-500 text-2xl mb-2"></i>
                <h3 class="font-semibold text-gray-800 mb-1">添加新地址</h3>
                <p class="text-sm text-gray-600 mb-3">快速填写收货信息，享受便捷配送服务</p>
                <button class="bg-blue-500 text-white px-6 py-2 rounded-full text-sm font-semibold">
                    <i class="fas fa-plus mr-2"></i>新增收货地址
                </button>
            </div>
        </div>

        <!-- 地址管理说明 -->
        <div class="px-4 py-3">
            <div class="bg-white rounded-2xl p-4 shadow-sm">
                <h3 class="font-semibold text-gray-800 mb-3">地址管理说明</h3>
                <div class="space-y-2 text-sm text-gray-600">
                    <div class="flex items-start">
                        <i class="fas fa-info-circle text-blue-500 mr-2 mt-0.5"></i>
                        <p>您最多可以保存20个收货地址</p>
                    </div>
                    <div class="flex items-start">
                        <i class="fas fa-shield-alt text-green-500 mr-2 mt-0.5"></i>
                        <p>我们会严格保护您的隐私信息</p>
                    </div>
                    <div class="flex items-start">
                        <i class="fas fa-truck text-orange-500 mr-2 mt-0.5"></i>
                        <p>准确的地址信息有助于快速配送</p>
                    </div>
                    <div class="flex items-start">
                        <i class="fas fa-star text-yellow-500 mr-2 mt-0.5"></i>
                        <p>设置默认地址可以简化下单流程</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部操作区 -->
        <div class="fixed bottom-20 left-0 right-0 bg-white border-t border-gray-100 px-4 py-3 shadow-lg">
            <button class="w-full bg-blue-500 text-white py-3 rounded-full font-semibold flex items-center justify-center">
                <i class="fas fa-plus mr-2"></i>
                添加新地址
            </button>
        </div>
    </div>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天详情</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="bg-gray-100">
    <div class="page-content">
        <!-- 聊天头部 -->
        <div class="bg-white px-4 py-3 shadow-sm border-b border-gray-100">
            <div class="flex items-center">
                <i class="fas fa-arrow-left text-gray-600 mr-4"></i>
                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face" 
                     class="w-10 h-10 rounded-full mr-3">
                <div class="flex-1">
                    <h2 class="font-semibold text-gray-800">李小美</h2>
                    <p class="text-xs text-green-500">在线</p>
                </div>
                <div class="flex items-center space-x-3">
                    <i class="fas fa-phone text-gray-600"></i>
                    <i class="fas fa-video text-gray-600"></i>
                    <i class="fas fa-ellipsis-v text-gray-600"></i>
                </div>
            </div>
        </div>

        <!-- 聊天内容区域 -->
        <div class="flex-1 p-4 space-y-4 pb-20">
            <!-- 时间分隔 -->
            <div class="text-center">
                <span class="bg-gray-200 text-gray-600 px-3 py-1 rounded-full text-xs">今天 14:30</span>
            </div>

            <!-- 对方消息 -->
            <div class="flex items-start">
                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face" 
                     class="w-8 h-8 rounded-full mr-3 mt-1">
                <div class="flex-1 max-w-xs">
                    <div class="bg-white rounded-2xl rounded-tl-md p-3 shadow-sm">
                        <p class="text-sm text-gray-800">你好！看到你分享的那个包包了，真的很好看呢！</p>
                    </div>
                    <p class="text-xs text-gray-500 mt-1 ml-2">14:30</p>
                </div>
            </div>

            <!-- 自己的消息 -->
            <div class="flex items-start justify-end">
                <div class="flex-1 max-w-xs text-right">
                    <div class="bg-blue-500 text-white rounded-2xl rounded-tr-md p-3 inline-block">
                        <p class="text-sm">谢谢！这是我最近买的，质量真的很不错</p>
                    </div>
                    <p class="text-xs text-gray-500 mt-1 mr-2">14:31</p>
                </div>
                <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=40&h=40&fit=crop&crop=face" 
                     class="w-8 h-8 rounded-full ml-3 mt-1">
            </div>

            <!-- 对方消息 -->
            <div class="flex items-start">
                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face" 
                     class="w-8 h-8 rounded-full mr-3 mt-1">
                <div class="flex-1 max-w-xs">
                    <div class="bg-white rounded-2xl rounded-tl-md p-3 shadow-sm">
                        <p class="text-sm text-gray-800">能告诉我在哪里买的吗？我也想买一个</p>
                    </div>
                    <p class="text-xs text-gray-500 mt-1 ml-2">14:32</p>
                </div>
            </div>

            <!-- 商品卡片消息 -->
            <div class="flex items-start justify-end">
                <div class="flex-1 max-w-xs text-right">
                    <div class="bg-blue-500 rounded-2xl rounded-tr-md p-3 inline-block">
                        <div class="bg-white rounded-xl p-3 text-left">
                            <div class="flex items-center">
                                <img src="https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=60&h=60&fit=crop" 
                                     class="w-12 h-12 rounded-lg mr-3">
                                <div class="flex-1">
                                    <h4 class="font-semibold text-sm text-gray-800">时尚手提包</h4>
                                    <p class="text-xs text-gray-500">真皮材质，多色可选</p>
                                    <p class="text-red-500 font-bold text-sm">¥299</p>
                                </div>
                            </div>
                        </div>
                        <p class="text-sm text-white mt-2">就是这个，链接分享给你了</p>
                    </div>
                    <p class="text-xs text-gray-500 mt-1 mr-2">14:33</p>
                </div>
                <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=40&h=40&fit=crop&crop=face" 
                     class="w-8 h-8 rounded-full ml-3 mt-1">
            </div>

            <!-- 对方消息 -->
            <div class="flex items-start">
                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face" 
                     class="w-8 h-8 rounded-full mr-3 mt-1">
                <div class="flex-1 max-w-xs">
                    <div class="bg-white rounded-2xl rounded-tl-md p-3 shadow-sm">
                        <p class="text-sm text-gray-800">太好了！价格也很合理，我马上去看看</p>
                    </div>
                    <p class="text-xs text-gray-500 mt-1 ml-2">14:34</p>
                </div>
            </div>

            <!-- 图片消息 -->
            <div class="flex items-start">
                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face" 
                     class="w-8 h-8 rounded-full mr-3 mt-1">
                <div class="flex-1 max-w-xs">
                    <div class="bg-white rounded-2xl rounded-tl-md p-2 shadow-sm">
                        <img src="https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=200&h=150&fit=crop" 
                             class="w-full rounded-lg">
                    </div>
                    <p class="text-xs text-gray-500 mt-1 ml-2">14:35</p>
                </div>
            </div>

            <!-- 自己的消息 -->
            <div class="flex items-start justify-end">
                <div class="flex-1 max-w-xs text-right">
                    <div class="bg-blue-500 text-white rounded-2xl rounded-tr-md p-3 inline-block">
                        <p class="text-sm">哇，你的搭配真好看！</p>
                    </div>
                    <p class="text-xs text-gray-500 mt-1 mr-2">14:36</p>
                </div>
                <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=40&h=40&fit=crop&crop=face" 
                     class="w-8 h-8 rounded-full ml-3 mt-1">
            </div>

            <!-- 语音消息 -->
            <div class="flex items-start">
                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face" 
                     class="w-8 h-8 rounded-full mr-3 mt-1">
                <div class="flex-1 max-w-xs">
                    <div class="bg-white rounded-2xl rounded-tl-md p-3 shadow-sm">
                        <div class="flex items-center">
                            <i class="fas fa-play text-blue-500 mr-3"></i>
                            <div class="flex-1 h-6 bg-gray-200 rounded-full relative">
                                <div class="h-full w-1/3 bg-blue-500 rounded-full"></div>
                            </div>
                            <span class="text-xs text-gray-500 ml-3">0:15</span>
                        </div>
                    </div>
                    <p class="text-xs text-gray-500 mt-1 ml-2">14:37</p>
                </div>
            </div>

            <!-- 正在输入提示 -->
            <div class="flex items-start">
                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face" 
                     class="w-8 h-8 rounded-full mr-3 mt-1">
                <div class="flex-1 max-w-xs">
                    <div class="bg-white rounded-2xl rounded-tl-md p-3 shadow-sm">
                        <div class="flex items-center space-x-1">
                            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 输入栏 -->
        <div class="fixed bottom-20 left-0 right-0 bg-white border-t border-gray-100 px-4 py-3">
            <div class="flex items-center space-x-3">
                <i class="fas fa-microphone text-gray-600 text-lg"></i>
                <div class="flex-1 flex items-center bg-gray-100 rounded-full px-4 py-2">
                    <input type="text" placeholder="输入消息..." class="flex-1 bg-transparent outline-none text-sm">
                    <i class="fas fa-smile text-gray-400 ml-2"></i>
                </div>
                <i class="fas fa-plus-circle text-gray-600 text-lg"></i>
            </div>
        </div>
    </div>
</body>
</html>

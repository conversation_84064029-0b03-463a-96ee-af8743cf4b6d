@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义iOS风格样式 */
@layer base {
  * {
    -webkit-tap-highlight-color: transparent;
  }
  
  body {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, sans-serif;
    background-color: #000;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
  }
}

@layer components {
  /* iPhone 15 Pro 容器样式 */
  .iphone-container {
    width: 393px;
    height: 852px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 40px;
    padding: 8px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    position: relative;
    margin: 20px auto;
  }
  
  .iphone-screen {
    width: 100%;
    height: 100%;
    background: #000;
    border-radius: 32px;
    overflow: hidden;
    position: relative;
  }
  
  /* iOS状态栏 */
  .status-bar {
    height: 44px;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(20px);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    color: white;
    font-size: 14px;
    font-weight: 600;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 50;
  }
  
  /* Tab Bar 底部导航 */
  .tab-bar {
    height: 83px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border-top: 0.5px solid rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-around;
    align-items: flex-start;
    padding-top: 8px;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 50;
  }
  
  .tab-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    color: #8E8E93;
    font-size: 10px;
    font-weight: 500;
    transition: color 0.2s;
  }
  
  .tab-item.active {
    color: #007AFF;
  }
  
  .tab-item i {
    font-size: 24px;
  }
  
  /* 页面内容区域 */
  .page-content {
    height: 100%;
    padding-top: 44px;
    padding-bottom: 83px;
    overflow-y: auto;
    background: #F2F2F7;
  }
  
  /* iOS风格卡片 */
  .ios-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }
  
  /* iOS风格按钮 */
  .ios-button {
    background: #007AFF;
    color: white;
    border: none;
    border-radius: 10px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.2s;
  }
  
  .ios-button:hover {
    background: #0056CC;
    transform: translateY(-1px);
  }
  
  /* iOS风格输入框 */
  .ios-input {
    background: #F2F2F7;
    border: none;
    border-radius: 10px;
    padding: 12px 16px;
    font-size: 16px;
    width: 100%;
  }
  
  .ios-input:focus {
    outline: none;
    background: white;
    box-shadow: 0 0 0 2px #007AFF;
  }
}

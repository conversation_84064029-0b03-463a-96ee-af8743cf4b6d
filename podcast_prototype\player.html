<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>Player - Podcast Prototype</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" />
  <style> body{font-family:Inter,system-ui;background:linear-gradient(180deg,#0f172a,#0b1220);color:#fff;} .status{height:44px;display:flex;align-items:center;justify-content:space-between;padding:0 12px;color:#94a3b8;} </style>
</head>
<body>
  <div class="h-screen flex flex-col items-center">
    <div class="w-[360px] mt-3">
      <div class="status"><div>9:41</div><div><i class="fa-solid fa-wifi"></i><i class="fa-solid fa-battery-full"></i></div></div>

      <div class="mt-4 rounded-3xl bg-white/5 p-4">
        <div class="flex items-center justify-between">
          <button class="p-2 bg-white/6 rounded-lg"><i class="fa-solid fa-arrow-left"></i></button>
          <div class="text-sm font-semibold">专辑 · 清晨故事</div>
          <button class="p-2 bg-white/6 rounded-lg"><i class="fa-solid fa-ellipsis"></i></button>
        </div>

        <div class="mt-4 flex flex-col items-center">
          <div class="w-64 h-64 rounded-2xl overflow-hidden shadow-lg">
            <img src="https://images.unsplash.com/photo-1521737604893-d14cc237f11d?q=80&w=1200&auto=format&fit=crop" class="w-full h-full object-cover">
          </div>
          <div class="mt-4 text-center">
            <div class="text-lg font-semibold">如何提高专注力</div>
            <div class="text-sm text-slate-300 mt-1">主讲：李老师 · 45 分钟</div>
          </div>

          <!-- progress -->
          <div class="w-full mt-6">
            <div class="flex items-center justify-between text-xs text-slate-300"><div>02:15</div><div>45:00</div></div>
            <div class="mt-2 h-1 bg-white/20 rounded-full overflow-hidden">
              <div style="width:32%" class="h-1 bg-indigo-500"></div>
            </div>
          </div>

          <!-- controls -->
          <div class="mt-6 flex items-center gap-6">
            <button class="p-3 rounded-full bg-white/6"><i class="fa-solid fa-backward-step"></i></button>
            <button class="p-4 rounded-full bg-indigo-500 text-white text-2xl shadow"><i class="fa-solid fa-pause"></i></button>
            <button class="p-3 rounded-full bg-white/6"><i class="fa-solid fa-forward-step"></i></button>
          </div>

          <div class="mt-6 w-full flex justify-around text-sm text-slate-300">
            <button><i class="fa-regular fa-heart"></i><div class="text-xs">收藏</div></button>
            <button><i class="fa-solid fa-share-nodes"></i><div class="text-xs">分享</div></button>
            <button><i class="fa-solid fa-download"></i><div class="text-xs">下载</div></button>
            <button><i class="fa-solid fa-comment"></i><div class="text-xs">评论</div></button>
          </div>

        </div>
      </div>

      <div class="mt-4 text-sm text-slate-300">播放列表</div>
      <div class="mt-2 space-y-2">
        <div class="bg-white/5 rounded-xl p-3 flex items-center gap-3">
          <div class="w-12 h-12 rounded-lg overflow-hidden"><img src="https://images.unsplash.com/photo-1500530855697-b586d89ba3ee?q=80&w=800&auto=format&fit=crop" class="w-full h-full object-cover"/></div>
          <div class="flex-1">
            <div class="text-sm font-medium">如何提高专注力</div>
            <div class="text-xs text-slate-400">02:15</div>
          </div>
          <div class="text-indigo-400"><i class="fa-solid fa-music"></i></div>
        </div>
        <div class="bg-white/6 rounded-xl p-3 flex items-center gap-3">
          <div class="w-12 h-12 rounded-lg overflow-hidden"><img src="https://images.unsplash.com/photo-1461749280684-dccba630e2f6?q=80&w=800&auto=format&fit=crop" class="w-full h-full object-cover"/></div>
          <div class="flex-1">
            <div class="text-sm opacity-80">夜读·经典名著</div>
            <div class="text-xs text-slate-400">30:00</div>
          </div>
        </div>
      </div>

    </div>
  </div>
</body>
</html>
# 社交购物商城APP - 高保真原型

## 项目简介

这是一个完整的社交购物商城APP高保真原型，采用现代化的设计理念，完美模拟iPhone 15 Pro的显示效果。项目融合了电商购物和社交互动功能，为用户提供全新的购物体验。

## 🎯 项目特色

### 📱 真实设备模拟
- **iPhone 15 Pro 尺寸**: 393x852px 完美还原
- **iOS 状态栏**: 包含时间、信号、WiFi、电池等真实元素
- **圆角设计**: 40px 外框圆角，32px 屏幕圆角
- **渐变背景**: 模拟真实设备的金属质感

### 🎨 iOS 设计规范
- **SF Pro 字体系统**: 使用苹果官方字体栈
- **iOS 色彩系统**: #007AFF 蓝色、#FF3B30 红色等标准色彩
- **毛玻璃效果**: backdrop-filter 实现真实的模糊效果
- **阴影系统**: 符合 iOS 设计的多层次阴影

### 🛍️ 功能完整性
- **购物功能**: 商品浏览、搜索、购物车、订单管理
- **社交功能**: 用户互动、内容分享、聊天系统
- **个人中心**: 会员系统、订单管理、设置功能

## 📁 项目结构

```
shop/
├── index.html                 # 主入口页面
├── package.json              # 项目配置
├── tailwind.config.js        # Tailwind CSS 配置
├── assets/
│   └── css/
│       └── style.css         # 自定义样式
├── js/
│   └── main.js              # 主要JavaScript逻辑
└── components/              # 页面组件
    ├── home.html           # 首页 (统一信息流)
    ├── discover.html       # 发现页
    ├── social.html         # 社交消息
    ├── cart.html           # 购物车
    ├── profile.html        # 个人中心
    ├── product-detail.html # 商品详情
    ├── chat-detail.html    # 聊天详情
    ├── search.html         # 搜索页
    ├── settings.html       # 设置页
    ├── checkout.html       # 结算页
    ├── my-orders.html      # 我的订单
    ├── address.html        # 收货地址
    └── coupons.html        # 优惠券
```

## 🚀 快速开始

### 方法一：直接打开
1. 下载项目文件
2. 直接用浏览器打开 `index.html`

### 方法二：本地服务器
1. 安装依赖：
   ```bash
   npm install
   ```

2. 启动开发服务器：
   ```bash
   npm run dev
   ```

3. 打开浏览器访问：`http://localhost:3000`

## 📱 界面展示

### 核心界面
1. **首页 (Home)**: 社交动态与商品推荐统一信息流展示
2. **发现 (Discover)**: 内容分类浏览 + 热门话题
3. **社交 (Social)**: 聊天列表 + 好友推荐
4. **购物车 (Cart)**: 商品管理 + 优惠券系统
5. **个人中心 (Profile)**: 会员等级 + 订单管理

### 详情界面
6. **商品详情**: 图片轮播 + 社交推荐 + 用户评价
7. **聊天详情**: 实时聊天 + 商品分享 + 语音消息
8. **搜索页**: 智能搜索 + 筛选排序
9. **设置页**: 账户安全 + 通知设置 + 应用配置

### 购物流程界面
10. **结算页 (Checkout)**: 地址选择 + 支付方式 + 费用明细
11. **我的订单 (My Orders)**: 订单状态管理 + 物流跟踪
12. **收货地址 (Address)**: 地址管理 + 默认设置
13. **优惠券 (Coupons)**: 优惠券管理 + 使用规则

## 🛠️ 技术栈

- **前端框架**: Vue 3 (Composition API)
- **CSS框架**: Tailwind CSS 3.3+
- **图标库**: Font Awesome 6.4
- **字体**: SF Pro Display (Apple 系统字体)
- **图片来源**: Unsplash (高质量免费图片)

## 🎨 设计系统

### 颜色规范
```css
--ios-blue: #007AFF      /* 主色调 */
--ios-red: #FF3B30       /* 强调色 */
--ios-green: #34C759     /* 成功色 */
--ios-orange: #FF9500    /* 警告色 */
--ios-gray: #F2F2F7      /* 背景色 */
--ios-dark: #1C1C1E      /* 文字色 */
```

### 圆角规范
```css
--ios-radius: 10px       /* 小圆角 */
--ios-radius-lg: 16px    /* 大圆角 */
--iphone-radius: 40px    /* 设备圆角 */
```

### 间距规范
```css
--safe-top: 44px         /* 状态栏高度 */
--safe-bottom: 34px      /* Home指示器区域 */
--tab-bar: 83px          /* 底部导航高度 */
```

## 📋 功能清单

### ✅ 已完成功能
- [x] iPhone 15 Pro 完美适配
- [x] iOS 设计规范实现
- [x] 13个完整界面设计
- [x] 统一信息流设计 (首页)
- [x] 完整购物流程 (结算、订单、地址、优惠券)
- [x] 真实图片和内容
- [x] 响应式布局
- [x] 状态栏和导航栏
- [x] 社交购物融合
- [x] 现代化UI组件

### 🔄 可扩展功能
- [ ] 页面间跳转动画
- [ ] 手势操作支持
- [ ] 深色模式适配
- [ ] 多语言支持
- [ ] 数据持久化
- [ ] 真实API集成

## 🎯 使用场景

### 产品设计
- 产品原型展示
- 用户体验测试
- 设计评审参考
- 客户需求沟通

### 开发参考
- 前端开发指南
- UI组件库参考
- 交互逻辑实现
- 响应式设计方案

### 学习研究
- iOS设计规范学习
- Vue.js实践项目
- Tailwind CSS应用
- 移动端适配方案

## 📞 技术支持

如果您在使用过程中遇到任何问题，或者有改进建议，欢迎通过以下方式联系：

- 项目地址：本地文件系统
- 技术栈：Vue 3 + Tailwind CSS + Font Awesome
- 兼容性：现代浏览器 (Chrome 90+, Safari 14+, Firefox 88+)

## 📄 许可证

本项目仅用于学习和展示目的，所有图片资源来源于 Unsplash，遵循其使用条款。

---

**注意**: 这是一个高保真原型项目，专为展示和开发参考而设计。所有界面和功能都经过精心设计，确保符合现代移动应用的用户体验标准。

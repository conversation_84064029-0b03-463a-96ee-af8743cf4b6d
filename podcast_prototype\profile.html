<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>Profile - Podcast Prototype</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" />
  <style> body{font-family:Inter,system-ui;background:#f8fafc;} .status{height:44px;display:flex;align-items:center;justify-content:space-between;padding:0 12px;color:#475569;} </style>
</head>
<body>
  <div class="h-screen">
    <div class="status"><div>9:41</div><div><i class="fa-solid fa-battery-full"></i></div></div>

    <div class="p-4">
      <div class="flex items-center gap-3">
        <img src="https://images.unsplash.com/photo-1544005313-94ddf0286df2?q=80&w=200&auto=format&fit=crop" class="w-16 h-16 rounded-full object-cover"/>
        <div>
          <div class="font-semibold text-lg">听友小明</div>
          <div class="text-xs text-slate-500">订阅 12 · 收藏 34</div>
        </div>
        <div class="ml-auto">
          <button class="bg-indigo-600 text-white px-3 py-2 rounded-lg text-sm">编辑</button>
        </div>
      </div>

      <div class="mt-4 grid grid-cols-2 gap-3">
        <div class="bg-white rounded-2xl p-3 shadow">
          <div class="text-xs text-slate-500">时长 (本月)</div>
          <div class="text-lg font-semibold mt-2">24h 12m</div>
        </div>
        <div class="bg-white rounded-2xl p-3 shadow">
          <div class="text-xs text-slate-500">播放次数</div>
          <div class="text-lg font-semibold mt-2">1.2k</div>
        </div>
      </div>

      <div class="mt-4">
        <div class="flex items-center justify-between mb-2">
          <div class="font-semibold">我的订阅</div>
          <div class="text-xs text-slate-500">管理</div>
        </div>
        <div class="space-y-2">
          <div class="flex items-center gap-3 bg-white p-3 rounded-2xl shadow">
            <img src="https://images.unsplash.com/photo-1518779578993-ec3579fee39f?q=80&w=800&auto=format&fit=crop" class="w-12 h-12 rounded-lg object-cover"/>
            <div class="flex-1">
              <div class="font-medium">清晨故事</div>
              <div class="text-xs text-slate-500">更新到 第240期</div>
            </div>
            <button class="text-indigo-600"><i class="fa-solid fa-bell"></i></button>
          </div>

          <div class="flex items-center gap-3 bg-white p-3 rounded-2xl shadow">
            <img src="https://images.unsplash.com/photo-1497215728101-856f4ea42174?q=80&w=800&auto=format&fit=crop" class="w-12 h-12 rounded-lg object-cover"/>
            <div class="flex-1">
              <div class="font-medium">科技漫谈</div>
              <div class="text-xs text-slate-500">更新到 第8期</div>
            </div>
            <button class="text-slate-500"><i class="fa-solid fa-bell-slash"></i></button>
          </div>
        </div>
      </div>

      <div class="mt-4">
        <div class="flex items-center justify-between mb-2">
          <div class="font-semibold">播放历史</div>
          <div class="text-xs text-slate-500">全部</div>
        </div>
        <div class="space-y-2">
          <div class="bg-white p-3 rounded-2xl shadow flex items-center gap-3">
            <div class="w-12 h-12 rounded-lg overflow-hidden"><img src="https://images.unsplash.com/photo-1500530855697-b586d89ba3ee?q=80&w=800&auto=format&fit=crop" class="w-full h-full object-cover"/></div>
            <div class="flex-1">
              <div class="text-sm">如何提高专注力</div>
              <div class="text-xs text-slate-500">02:15 · 昨天</div>
            </div>
            <div class="text-slate-400"><i class="fa-solid fa-ellipsis"></i></div>
          </div>
        </div>
      </div>

    </div>

    <div class="border-t p-3">
      <div class="flex justify-around text-slate-600">
        <div class="flex flex-col items-center"><i class="fa-solid fa-house"></i><div class="text-xs">首页</div></div>
        <div class="flex flex-col items-center"><i class="fa-solid fa-compass"></i><div class="text-xs">发现</div></div>
        <div class="flex flex-col items-center"><i class="fa-solid fa-play-circle"></i><div class="text-xs">播放</div></div>
        <div class="flex flex-col items-center text-indigo-600"><i class="fa-solid fa-user"></i><div class="text-xs">我的</div></div>
      </div>
    </div>

  </div>
</body>
</html>
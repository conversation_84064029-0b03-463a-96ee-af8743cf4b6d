<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="bg-gray-50">
    <div class="page-content">
        <!-- 搜索栏 -->
        <div class="bg-white px-4 py-3 shadow-sm">
            <div class="flex items-center bg-gray-100 rounded-full px-4 py-2">
                <i class="fas fa-search text-gray-400 mr-3"></i>
                <input type="text" placeholder="搜索商品、品牌、用户..." class="flex-1 bg-transparent outline-none text-sm">
                <i class="fas fa-microphone text-gray-400 ml-2"></i>
            </div>
        </div>

        <!-- 轮播图 -->
        <div class="px-4 py-3">
            <div class="relative h-40 rounded-2xl overflow-hidden">
                <img src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=160&fit=crop" 
                     alt="Banner" class="w-full h-full object-cover">
                <div class="absolute inset-0 bg-gradient-to-r from-black/30 to-transparent"></div>
                <div class="absolute bottom-4 left-4 text-white">
                    <h3 class="text-lg font-bold">春季新品上市</h3>
                    <p class="text-sm opacity-90">限时优惠 8折起</p>
                </div>
                <div class="absolute bottom-4 right-4 flex space-x-1">
                    <div class="w-2 h-2 bg-white rounded-full"></div>
                    <div class="w-2 h-2 bg-white/50 rounded-full"></div>
                    <div class="w-2 h-2 bg-white/50 rounded-full"></div>
                </div>
            </div>
        </div>

        <!-- 分类导航 -->
        <div class="bg-white mx-4 rounded-2xl p-4 shadow-sm">
            <div class="grid grid-cols-4 gap-4">
                <div class="text-center">
                    <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-tshirt text-red-500 text-lg"></i>
                    </div>
                    <span class="text-xs text-gray-600">服装</span>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-mobile-alt text-blue-500 text-lg"></i>
                    </div>
                    <span class="text-xs text-gray-600">数码</span>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-home text-green-500 text-lg"></i>
                    </div>
                    <span class="text-xs text-gray-600">家居</span>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-gamepad text-purple-500 text-lg"></i>
                    </div>
                    <span class="text-xs text-gray-600">娱乐</span>
                </div>
            </div>
        </div>

        <!-- 统一信息流 -->
        <div class="px-4 py-3">
            <div class="flex items-center justify-between mb-3">
                <h2 class="text-lg font-bold text-gray-800">推荐</h2>
                <span class="text-sm text-blue-500">刷新</span>
            </div>

            <!-- 社交动态1 -->
            <div class="bg-white rounded-2xl p-4 shadow-sm mb-3">
                <div class="flex items-center mb-3">
                    <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face"
                         alt="User" class="w-10 h-10 rounded-full mr-3">
                    <div class="flex-1">
                        <h4 class="font-semibold text-sm">李小美</h4>
                        <p class="text-xs text-gray-500">2分钟前</p>
                    </div>
                    <i class="fas fa-ellipsis-h text-gray-400"></i>
                </div>
                <p class="text-sm text-gray-700 mb-3">刚收到这款包包，质量超好！推荐给大家～</p>
                <img src="https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=300&h=200&fit=crop"
                     alt="Product" class="w-full h-32 object-cover rounded-xl mb-3">
                <div class="flex items-center justify-between text-sm text-gray-500">
                    <div class="flex items-center space-x-4">
                        <span><i class="far fa-heart mr-1"></i>128</span>
                        <span><i class="far fa-comment mr-1"></i>23</span>
                        <span><i class="fas fa-share mr-1"></i>分享</span>
                    </div>
                    <span class="text-blue-500 font-semibold">¥299</span>
                </div>
            </div>

            <!-- 商品推荐1 -->
            <div class="bg-white rounded-2xl overflow-hidden shadow-sm mb-3">
                <div class="flex items-center p-3 border-b border-gray-100">
                    <div class="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center mr-2">
                        <i class="fas fa-fire text-orange-500 text-xs"></i>
                    </div>
                    <span class="text-sm text-gray-600">热门推荐</span>
                </div>
                <img src="https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=200&fit=crop"
                     alt="Product" class="w-full h-40 object-cover">
                <div class="p-4">
                    <h4 class="font-semibold text-base mb-2">时尚智能手表</h4>
                    <p class="text-sm text-gray-600 mb-3">1.4英寸高清屏 | 7天续航 | 50米防水</p>
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <span class="text-red-500 font-bold text-lg mr-2">¥599</span>
                            <span class="text-xs text-gray-400 line-through">¥899</span>
                        </div>
                        <div class="flex items-center text-xs text-gray-400">
                            <i class="fas fa-star text-yellow-400 mr-1"></i>4.8 (2.3k评价)
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=20&h=20&fit=crop&crop=face"
                                 class="w-4 h-4 rounded-full mr-1">
                            <span class="text-xs text-gray-500">张小明等156人购买</span>
                        </div>
                        <button class="bg-red-500 text-white px-4 py-1 rounded-full text-sm">立即购买</button>
                    </div>
                </div>
            </div>

            <!-- 社交动态2 -->
            <div class="bg-white rounded-2xl p-4 shadow-sm mb-3">
                <div class="flex items-center mb-3">
                    <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face"
                         alt="User" class="w-10 h-10 rounded-full mr-3">
                    <div class="flex-1">
                        <h4 class="font-semibold text-sm">美食家小丽</h4>
                        <p class="text-xs text-gray-500">15分钟前</p>
                    </div>
                    <i class="fas fa-ellipsis-h text-gray-400"></i>
                </div>
                <p class="text-sm text-gray-700 mb-3">今天用这个厨具做了超好吃的菜，强烈推荐！</p>
                <div class="grid grid-cols-3 gap-1 mb-3">
                    <img src="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=120&h=120&fit=crop"
                         class="w-full h-20 object-cover rounded-lg">
                    <img src="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=120&h=120&fit=crop"
                         class="w-full h-20 object-cover rounded-lg">
                    <img src="https://images.unsplash.com/photo-1574484284002-952d92456975?w=120&h=120&fit=crop"
                         class="w-full h-20 object-cover rounded-lg">
                </div>
                <div class="flex items-center justify-between text-sm text-gray-500">
                    <div class="flex items-center space-x-4">
                        <span><i class="far fa-heart mr-1"></i>89</span>
                        <span><i class="far fa-comment mr-1"></i>12</span>
                        <span><i class="fas fa-share mr-1"></i>分享</span>
                    </div>
                    <span class="text-blue-500 font-semibold">¥159</span>
                </div>
            </div>

            <!-- 商品推荐2 -->
            <div class="bg-white rounded-2xl overflow-hidden shadow-sm mb-3">
                <div class="flex items-center p-3 border-b border-gray-100">
                    <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                        <i class="fas fa-thumbs-up text-blue-500 text-xs"></i>
                    </div>
                    <span class="text-sm text-gray-600">好友推荐</span>
                </div>
                <img src="https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=400&h=200&fit=crop"
                     alt="Product" class="w-full h-40 object-cover">
                <div class="p-4">
                    <h4 class="font-semibold text-base mb-2">无线蓝牙耳机</h4>
                    <p class="text-sm text-gray-600 mb-3">主动降噪 | 30小时续航 | 快速充电</p>
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <span class="text-red-500 font-bold text-lg mr-2">¥299</span>
                            <span class="bg-red-100 text-red-600 px-2 py-1 rounded text-xs">限时优惠</span>
                        </div>
                        <div class="flex items-center text-xs text-gray-400">
                            <i class="fas fa-star text-yellow-400 mr-1"></i>4.9 (1.8k评价)
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=20&h=20&fit=crop&crop=face"
                                 class="w-4 h-4 rounded-full mr-1">
                            <span class="text-xs text-gray-500">李小美推荐</span>
                        </div>
                        <button class="bg-orange-500 text-white px-4 py-1 rounded-full text-sm">加购物车</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

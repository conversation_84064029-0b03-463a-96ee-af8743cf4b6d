<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8"/><meta name="viewport" content="width=device-width,initial-scale=1"/>
  <title>Profile - 我的</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css"/>
  <style> body{font-family:Inter,system-ui;background:#f8fafc;margin:0} .status{height:44px;display:flex;align-items:center;justify-content:space-between;padding:0 12px;color:#475569} </style>
</head>
<body class="h-screen flex flex-col">
  <div class="status"><div>9:41</div><div><i class="fa-solid fa-battery-full"></i></div></div>

  <main class="p-4 overflow-auto flex-1">
    <div class="flex items-center gap-3">
      <img src="https://images.unsplash.com/photo-1544005313-94ddf0286df2?q=80&w=200&auto=format&fit=crop" class="w-16 h-16 rounded-full object-cover"/>
      <div>
        <div class="text-lg font-semibold">用户小红</div>
        <div class="text-xs text-slate-500">普通会员 · 已注册 1 年</div>
      </div>
      <div class="ml-auto"><button class="bg-indigo-600 text-white px-3 py-2 rounded-lg text-sm">编辑资料</button></div>
    </div>

    <div class="mt-4 grid grid-cols-3 gap-3">
      <div class="bg-white p-3 rounded-2xl text-center shadow">
        <div class="font-semibold">12</div>
        <div class="text-xs text-slate-400">我的订单</div>
      </div>
      <div class="bg-white p-3 rounded-2xl text-center shadow">
        <div class="font-semibold">34</div>
        <div class="text-xs text-slate-400">收藏</div>
      </div>
      <div class="bg-white p-3 rounded-2xl text-center shadow">
        <div class="font-semibold">5</div>
        <div class="text-xs text-slate-400">优惠券</div>
      </div>
    </div>

    <div class="mt-4 space-y-3">
      <a href="#" class="block bg-white p-3 rounded-2xl shadow flex items-center gap-3">
        <i class="fa-solid fa-box-archive text-indigo-600"></i>
        <div class="flex-1">
          <div class="font-medium">我的订单</div>
          <div class="text-xs text-slate-400">查看全部订单</div>
        </div>
        <i class="fa-solid fa-chevron-right text-slate-400"></i>
      </a>

      <a href="#" class="block bg-white p-3 rounded-2xl shadow flex items-center gap-3">
        <i class="fa-solid fa-star text-amber-400"></i>
        <div class="flex-1">
          <div class="font-medium">我的收藏</div>
          <div class="text-xs text-slate-400">查看已收藏的商品和内容</div>
        </div>
        <i class="fa-solid fa-chevron-right text-slate-400"></i>
      </a>

      <a href="./settings.html" class="block bg-white p-3 rounded-2xl shadow flex items-center gap-3">
        <i class="fa-solid fa-gear text-slate-500"></i>
        <div class="flex-1">
          <div class="font-medium">设置</div>
          <div class="text-xs text-slate-400">账号与通知</div>
        </div>
        <i class="fa-solid fa-chevron-right text-slate-400"></i>
      </a>
    </div>
  </main>

  <footer class="border-t p-3 bg-white">
    <div class="flex justify-around text-slate-600">
      <div class="flex flex-col items-center"><i class="fa-solid fa-house"></i><div class="text-xs">首页</div></div>
      <div class="flex flex-col items-center"><i class="fa-solid fa-list"></i><div class="text-xs">分类</div></div>
      <div class="flex flex-col items-center"><i class="fa-solid fa-shopping-cart"></i><div class="text-xs">购物车</div></div>
      <div class="flex flex-col items-center"><i class="fa-solid fa-message"></i><div class="text-xs">消息</div></div>
      <div class="flex flex-col items-center text-indigo-600"><i class="fa-solid fa-user"></i><div class="text-xs">我的</div></div>
    </div>
  </footer>
</body>
</html>
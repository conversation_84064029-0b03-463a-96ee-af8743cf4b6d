<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8"/><meta name="viewport" content="width=device-width,initial-scale=1"/>
  <title>Category - 社交购物商城</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css"/>
  <style> body{font-family:Inter,system-ui;background:#fff;margin:0} .status{height:44px;display:flex;align-items:center;justify-content:space-between;padding:0 12px;color:#475569} </style>
</head>
<body class="h-screen flex flex-col">
  <div class="status"><div>9:41</div><div><i class="fa-solid fa-wifi"></i><i class="fa-solid fa-battery-full"></i></div></div>
  <header class="p-4">
    <div class="flex items-center gap-3">
      <button class="p-2 bg-slate-100 rounded-lg"><i class="fa-solid fa-arrow-left"></i></button>
      <div class="flex-1">
        <div class="text-lg font-semibold">分类</div>
        <div class="text-xs text-slate-400">发现你感兴趣的类目</div>
      </div>
      <button class="p-2 bg-slate-100 rounded-lg"><i class="fa-solid fa-filter"></i></button>
    </div>
  </header>

  <main class="p-4 overflow-auto flex-1">
    <div class="grid grid-cols-3 gap-3">
      <div class="bg-gradient-to-br from-pink-400 to-rose-500 text-white rounded-2xl p-4 flex flex-col items-start">
        <i class="fa-solid fa-mug-saucer fa-xl"></i>
        <div class="mt-3 font-semibold">家居</div>
      </div>
      <div class="bg-gradient-to-br from-indigo-400 to-blue-500 text-white rounded-2xl p-4 flex flex-col items-start">
        <i class="fa-solid fa-shirt fa-xl"></i>
        <div class="mt-3 font-semibold">服饰</div>
      </div>
      <div class="bg-gradient-to-br from-emerald-400 to-teal-500 text-white rounded-2xl p-4 flex flex-col items-start">
        <i class="fa-solid fa-bolt fa-xl"></i>
        <div class="mt-3 font-semibold">数码</div>
      </div>
      <div class="bg-white rounded-2xl p-3 shadow col-span-3 mt-3">
        <div class="flex items-center justify-between mb-3">
          <div class="font-semibold">热门筛选</div>
          <div class="text-sm text-slate-400">清除</div>
        </div>
        <div class="flex gap-2 flex-wrap">
          <button class="px-3 py-1 bg-slate-100 rounded-full text-sm">价格</button>
          <button class="px-3 py-1 bg-slate-100 rounded-full text-sm">销量</button>
          <button class="px-3 py-1 bg-slate-100 rounded-full text-sm">新品</button>
          <button class="px-3 py-1 bg-slate-100 rounded-full text-sm">优惠</button>
        </div>
      </div>

      <div class="col-span-3 mt-4">
        <div class="text-sm font-semibold mb-3">本类推荐</div>
        <div class="grid grid-cols-2 gap-3">
          <div class="bg-white rounded-2xl p-3 shadow">
            <img src="https://images.unsplash.com/photo-1600180758890-1b1f9e5d6f0e?q=80&w=800&auto=format&fit=crop" class="w-full h-36 object-cover rounded-lg"/>
            <div class="mt-3 flex justify-between items-center">
              <div>
                <div class="font-medium text-sm">便携榨汁机</div>
                <div class="text-xs text-slate-400">¥129</div>
              </div>
              <button class="bg-indigo-600 text-white px-2 py-1 rounded-lg"><i class="fa-solid fa-cart-plus"></i></button>
            </div>
          </div>
          <div class="bg-white rounded-2xl p-3 shadow">
            <img src="https://images.unsplash.com/photo-1580910051071-4e1f3a7e7d7b?q=80&w=800&auto=format&fit=crop" class="w-full h-36 object-cover rounded-lg"/>
            <div class="mt-3 flex justify-between items-center">
              <div>
                <div class="font-medium text-sm">蓝牙降噪耳机</div>
                <div class="text-xs text-slate-400">¥399</div>
              </div>
              <button class="bg-indigo-600 text-white px-2 py-1 rounded-lg"><i class="fa-solid fa-cart-plus"></i></button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>

  <footer class="border-t p-3">
    <div class="flex justify-around text-slate-600">
      <div class="flex flex-col items-center"><i class="fa-solid fa-house"></i><div class="text-xs">首页</div></div>
      <div class="flex flex-col items-center text-indigo-600"><i class="fa-solid fa-list"></i><div class="text-xs">分类</div></div>
      <div class="flex flex-col items-center"><i class="fa-solid fa-shopping-cart"></i><div class="text-xs">购物车</div></div>
      <div class="flex flex-col items-center"><i class="fa-solid fa-message"></i><div class="text-xs">消息</div></div>
      <div class="flex flex-col items-center"><i class="fa-solid fa-user"></i><div class="text-xs">我的</div></div>
    </div>
  </footer>
</body>
</html>